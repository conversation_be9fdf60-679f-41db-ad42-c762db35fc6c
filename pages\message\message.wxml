<!--pages/message/message.wxml-->
<view class="container">
  <!-- 标签切换 -->
  <view class="tab-bar">
    <view class="tab-item {{currentTab === 'member' ? 'active' : ''}}" bindtap="switchTab" data-tab="member">
      我是团员
    </view>
    <view class="tab-item {{currentTab === 'notice' ? 'active' : ''}}" bindtap="switchTab" data-tab="notice">
      通知
    </view>
  </view>

  <!-- 团员消息页面 -->
  <view class="member-page" wx:if="{{currentTab === 'member'}}">
    <!-- 搜索栏 -->
    <view class="search-bar">
      <view class="search-input">
        <icon class="search-icon" type="search" size="16" color="#999"></icon>
        <input class="search-field" placeholder="搜索快团团昵称/微信昵称" value="{{searchKeyword}}" bindinput="onSearchInput" />
      </view>
    </view>

    <!-- 功能图标 -->
    <view class="function-icons">
      <view class="icon-item" bindtap="goToLogistics">
        <view class="icon-wrapper" style="background-color: #07c160;">
          <text class="icon-text">🚚</text>
        </view>
        <text class="icon-label">物流助手</text>
      </view>
      <view class="icon-item" bindtap="goToMarketing">
        <view class="icon-wrapper" style="background-color: #ff976a;">
          <text class="icon-text">📦</text>
        </view>
        <text class="icon-label">营销通知</text>
      </view>
      <view class="icon-item" bindtap="goToPromotion">
        <view class="icon-wrapper" style="background-color: #ff6b6b;">
          <text class="icon-text">¥</text>
        </view>
        <text class="icon-label">优惠消息</text>
      </view>
      <view class="icon-item" bindtap="goToSystem">
        <view class="icon-wrapper" style="background-color: #4ecdc4;">
          <text class="icon-text">🔔</text>
        </view>
        <text class="icon-label">系统消息</text>
      </view>
    </view>

    <!-- 推荐关注 -->
    <view class="recommend-section">
      <view class="section-header">
        <text class="section-title">猜你喜欢</text>
        <text class="section-subtitle">仅展示已关注团长的团购</text>
      </view>
      
      <view class="recommend-item" wx:for="{{recommendGroups}}" wx:key="id">
        <image class="recommend-avatar" src="{{item.avatar}}" mode="aspectFill"></image>
        <view class="recommend-info">
          <view class="recommend-header">
            <text class="recommend-title">{{item.title}}</text>
            <button class="subscribe-btn" size="mini" bindtap="subscribeGroup" data-id="{{item.id}}">+订阅</button>
          </view>
          <text class="recommend-subtitle">{{item.subtitle}}</text>
        </view>
      </view>
      
      <view class="recommend-product">
        <image class="product-image" src="{{featuredProduct.image}}" mode="aspectFill"></image>
        <view class="product-info">
          <text class="product-title">{{featuredProduct.title}}</text>
          <view class="product-stats">
            <text class="product-participants">{{featuredProduct.participants}}人团购</text>
            <text class="product-views">{{featuredProduct.views}}人看过</text>
          </view>
          <view class="product-actions">
            <button class="share-btn" size="mini" bindtap="shareProduct">分享</button>
            <button class="buy-btn" size="mini" bindtap="buyProduct">去购买</button>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 通知页面 -->
  <view class="notice-page" wx:if="{{currentTab === 'notice'}}">
    <!-- 消息列表 -->
    <view class="message-list">
      <view class="message-item {{!item.read ? 'unread' : ''}}" wx:for="{{messages}}" wx:key="id" bindtap="readMessage" data-id="{{item.id}}">
        <image class="message-avatar" src="{{item.avatar}}" mode="aspectFill"></image>
        <view class="message-content">
          <view class="message-header">
            <text class="message-title">{{item.title}}</text>
            <text class="message-time">{{item.timeText}}</text>
          </view>
          <text class="message-text">{{item.content}}</text>
        </view>
        <view class="unread-dot" wx:if="{{!item.read}}"></view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{messages.length === 0}}">
      <image class="empty-image" src="/images/empty-message.png"></image>
      <text class="empty-text">暂无消息</text>
    </view>
  </view>
</view>
