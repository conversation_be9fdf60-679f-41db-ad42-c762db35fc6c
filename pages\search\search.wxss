/* pages/search/search.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 搜索栏 */
.search-bar {
  display: flex;
  align-items: center;
  background-color: white;
  padding: 20rpx;
  border-bottom: 1rpx solid #e5e5e5;
}

.search-input {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 50rpx;
  padding: 15rpx 20rpx;
  margin-right: 20rpx;
}

.search-icon {
  margin-right: 10rpx;
}

.search-field {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}

.cancel-btn {
  color: #07c160;
  font-size: 28rpx;
}

/* 搜索历史 */
.search-history {
  background-color: white;
  margin: 20rpx;
  border-radius: 10rpx;
  padding: 30rpx;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.history-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
}

.clear-btn {
  color: #999999;
  font-size: 24rpx;
}

.history-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.history-tag {
  background-color: #f5f5f5;
  color: #666666;
  padding: 10rpx 20rpx;
  border-radius: 25rpx;
  font-size: 24rpx;
}

/* 热门搜索 */
.hot-search {
  background-color: white;
  margin: 20rpx;
  border-radius: 10rpx;
  padding: 30rpx;
}

.hot-header {
  margin-bottom: 20rpx;
}

.hot-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
}

.hot-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.hot-tag {
  background-color: #fff3e0;
  color: #ff976a;
  padding: 10rpx 20rpx;
  border-radius: 25rpx;
  font-size: 24rpx;
  border: 1rpx solid #ff976a;
}

/* 搜索结果 */
.search-results {
  padding: 0 20rpx;
}

.result-item {
  display: flex;
  background-color: white;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.result-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
}

.result-info {
  flex: 1;
}

.result-title {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.4;
  margin-bottom: 15rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.result-price {
  margin-bottom: 10rpx;
}

.current-price {
  color: #fa5151;
  font-size: 30rpx;
  font-weight: bold;
  margin-right: 15rpx;
}

.original-price {
  color: #999999;
  font-size: 24rpx;
  text-decoration: line-through;
}

.result-sales {
  color: #999999;
  font-size: 24rpx;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 20rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  color: #999999;
  font-size: 28rpx;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 100rpx 20rpx;
}

.loading-text {
  color: #999999;
  font-size: 28rpx;
}
