# 快团团微信小程序

一个功能完整的团购微信小程序，模仿快团团的核心功能。

## 功能特性

### 🏠 首页功能
- 商品列表展示
- 分类筛选
- 搜索功能
- 轮播图展示
- 推荐商品

### 🛍️ 商品功能
- 商品详情展示
- 规格选择
- 加入购物车
- 立即购买
- 商品分享

### 📦 订单管理
- 订单列表
- 订单状态管理（待支付、发货、售后等）
- 订单搜索
- 团长推荐商品

### 🎯 开团功能
- 普通团购
- 预售团购
- 卡券团购
- 报名团购
- 新手引导

### 💬 消息中心
- 团员消息
- 系统通知
- 物流助手
- 营销通知
- 优惠消息

### 👤 个人中心
- 用户信息管理
- 我的订单
- 我的收藏
- 收货地址
- 钱包管理
- 优惠券
- 积分商城

## 项目结构

```
├── app.js                 # 小程序入口文件
├── app.json              # 小程序配置文件
├── app.wxss              # 全局样式文件
├── sitemap.json          # 搜索配置文件
├── project.config.json   # 项目配置文件
├── pages/                # 页面目录
│   ├── index/           # 首页
│   ├── product/         # 商品详情页
│   ├── order/           # 订单页
│   ├── create-group/    # 开团页
│   ├── message/         # 消息页
│   ├── profile/         # 个人中心
│   ├── search/          # 搜索页
│   ├── login/           # 登录页
│   ├── category/        # 分类页
│   └── cart/            # 购物车页
├── components/          # 组件目录
│   └── product-card/    # 商品卡片组件
├── utils/               # 工具函数
│   └── util.js         # 通用工具函数
├── data/                # 数据目录
│   └── mock.js         # 模拟数据
└── images/              # 图片资源
    └── README.md       # 图片资源说明
```

## 技术栈

- **框架**: 微信小程序原生框架
- **样式**: WXSS (类似CSS)
- **脚本**: JavaScript ES6+
- **数据**: 本地模拟数据 (可替换为真实API)

## 开发环境

1. **微信开发者工具**: 下载并安装微信开发者工具
2. **Node.js**: 建议使用 Node.js 14+ 版本

## 快速开始

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd ktt
   ```

2. **导入项目**
   - 打开微信开发者工具
   - 选择"导入项目"
   - 选择项目目录
   - 填入测试AppID或使用测试号

3. **配置AppID**
   - 在 `project.config.json` 中修改 `appid` 字段
   - 替换为你的小程序AppID

4. **运行项目**
   - 在微信开发者工具中点击"编译"
   - 在模拟器中预览效果

## 主要页面说明

### 首页 (pages/index)
- 展示商品列表
- 支持分类筛选
- 轮播图展示
- 搜索功能入口

### 商品详情页 (pages/product)
- 商品图片轮播
- 商品信息展示
- 规格选择弹窗
- 购买操作

### 订单页 (pages/order)
- 订单状态筛选
- 订单列表展示
- 订单操作（支付、取消、确认收货）

### 开团页 (pages/create-group)
- 团购类型选择
- 用户类型选择
- 开团引导

### 消息页 (pages/message)
- 团员/通知切换
- 消息列表
- 推荐关注

### 个人中心 (pages/profile)
- 用户信息展示
- 功能菜单
- 设置选项

## 数据结构

项目使用模拟数据，主要数据结构包括：

- **商品数据**: 包含商品信息、价格、规格等
- **订单数据**: 包含订单状态、商品列表、价格等
- **用户数据**: 包含用户信息、权限等
- **消息数据**: 包含消息类型、内容、时间等

## 自定义配置

### 修改主题色
在 `app.wxss` 中修改 CSS 变量：
```css
:root {
  --primary-color: #07c160;  /* 主色调 */
  --danger-color: #fa5151;   /* 危险色 */
  --warning-color: #ff976a;  /* 警告色 */
}
```

### 添加新页面
1. 在 `pages` 目录下创建新页面文件夹
2. 在 `app.json` 的 `pages` 数组中添加页面路径
3. 创建对应的 `.wxml`, `.wxss`, `.js`, `.json` 文件

### 添加新组件
1. 在 `components` 目录下创建组件文件夹
2. 创建组件的四个文件
3. 在需要使用的页面的 `.json` 文件中引入组件

## API 集成

项目目前使用模拟数据，要集成真实API：

1. 修改 `app.js` 中的 `globalData.baseUrl`
2. 在各页面的 `.js` 文件中替换模拟数据调用
3. 处理API错误和加载状态

## 部署发布

1. **代码审查**: 确保代码质量和功能完整性
2. **测试**: 在不同设备上测试功能
3. **上传代码**: 在微信开发者工具中上传代码
4. **提交审核**: 在微信公众平台提交审核
5. **发布上线**: 审核通过后发布上线

## 注意事项

1. **图片资源**: 需要准备相应的图片资源文件
2. **API权限**: 确保小程序有相应的API调用权限
3. **支付功能**: 需要开通微信支付并配置相关参数
4. **用户授权**: 处理用户信息授权和隐私保护

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请通过以下方式联系：
- 邮箱: <EMAIL>
- 微信: your-wechat-id
