<!--pages/search/search.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input">
      <icon class="search-icon" type="search" size="16" color="#999"></icon>
      <input class="search-field" placeholder="搜索商品" value="{{keyword}}" bindinput="onInput" bindconfirm="onSearch" focus="{{true}}" />
    </view>
    <text class="cancel-btn" bindtap="goBack">取消</text>
  </view>

  <!-- 搜索历史 -->
  <view class="search-history" wx:if="{{!keyword && searchHistory.length > 0}}">
    <view class="history-header">
      <text class="history-title">搜索历史</text>
      <text class="clear-btn" bindtap="clearHistory">清空</text>
    </view>
    <view class="history-tags">
      <text class="history-tag" wx:for="{{searchHistory}}" wx:key="*this" bindtap="searchHistoryItem" data-keyword="{{item}}">{{item}}</text>
    </view>
  </view>

  <!-- 热门搜索 -->
  <view class="hot-search" wx:if="{{!keyword}}">
    <view class="hot-header">
      <text class="hot-title">热门搜索</text>
    </view>
    <view class="hot-tags">
      <text class="hot-tag" wx:for="{{hotKeywords}}" wx:key="*this" bindtap="searchHotItem" data-keyword="{{item}}">{{item}}</text>
    </view>
  </view>

  <!-- 搜索结果 -->
  <view class="search-results" wx:if="{{keyword && searchResults.length > 0}}">
    <view class="result-item" wx:for="{{searchResults}}" wx:key="id" bindtap="goToProduct" data-id="{{item.id}}">
      <image class="result-image" src="{{item.image}}" mode="aspectFill"></image>
      <view class="result-info">
        <text class="result-title">{{item.title}}</text>
        <view class="result-price">
          <text class="current-price">¥{{item.price}}</text>
          <text class="original-price" wx:if="{{item.originalPrice}}">¥{{item.originalPrice}}</text>
        </view>
        <text class="result-sales">已售{{item.sales}}份</text>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{keyword && searchResults.length === 0 && !loading}}">
    <image class="empty-image" src="/images/empty-search.png"></image>
    <text class="empty-text">没有找到相关商品</text>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <text class="loading-text">搜索中...</text>
  </view>
</view>
