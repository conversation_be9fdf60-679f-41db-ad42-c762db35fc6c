// API服务
const config = require('../config/config.js')

class ApiService {
  constructor() {
    this.baseUrl = config.api.baseUrl
    this.timeout = config.api.timeout
  }

  // 通用请求方法
  request(options) {
    return new Promise((resolve, reject) => {
      const {
        url,
        method = 'GET',
        data = {},
        header = {},
        showLoading = false,
        loadingText = '加载中...'
      } = options

      if (showLoading) {
        wx.showLoading({
          title: loadingText,
          mask: true
        })
      }

      wx.request({
        url: this.baseUrl + url,
        method: method,
        data: data,
        header: {
          'content-type': 'application/json',
          'Authorization': wx.getStorageSync('token') || '',
          ...header
        },
        timeout: this.timeout,
        success: (res) => {
          if (showLoading) {
            wx.hideLoading()
          }

          if (res.statusCode === 200) {
            const { code, data, message } = res.data
            if (code === config.errorCodes.SUCCESS) {
              resolve(data)
            } else {
              this.handleError(code, message)
              reject({ code, message })
            }
          } else {
            this.handleHttpError(res.statusCode)
            reject(res)
          }
        },
        fail: (error) => {
          if (showLoading) {
            wx.hideLoading()
          }
          this.handleNetworkError(error)
          reject(error)
        }
      })
    })
  }

  // GET请求
  get(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'GET',
      data,
      ...options
    })
  }

  // POST请求
  post(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'POST',
      data,
      ...options
    })
  }

  // PUT请求
  put(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'PUT',
      data,
      ...options
    })
  }

  // DELETE请求
  delete(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'DELETE',
      data,
      ...options
    })
  }

  // 上传文件
  uploadFile(filePath, url = '/upload', formData = {}) {
    return new Promise((resolve, reject) => {
      wx.showLoading({
        title: '上传中...',
        mask: true
      })

      wx.uploadFile({
        url: this.baseUrl + url,
        filePath: filePath,
        name: 'file',
        formData: formData,
        header: {
          'Authorization': wx.getStorageSync('token') || ''
        },
        success: (res) => {
          wx.hideLoading()
          try {
            const data = JSON.parse(res.data)
            if (data.code === config.errorCodes.SUCCESS) {
              resolve(data.data)
            } else {
              this.handleError(data.code, data.message)
              reject(data)
            }
          } catch (e) {
            getApp().showToast('上传失败')
            reject(e)
          }
        },
        fail: (error) => {
          wx.hideLoading()
          getApp().showToast('上传失败')
          reject(error)
        }
      })
    })
  }

  // 处理业务错误
  handleError(code, message) {
    switch (code) {
      case config.errorCodes.UNAUTHORIZED:
        this.handleUnauthorized()
        break
      case config.errorCodes.FORBIDDEN:
        getApp().showToast('权限不足')
        break
      case config.errorCodes.NOT_FOUND:
        getApp().showToast('资源不存在')
        break
      case config.errorCodes.SERVER_ERROR:
        getApp().showToast('服务器错误')
        break
      default:
        getApp().showToast(message || '请求失败')
    }
  }

  // 处理HTTP错误
  handleHttpError(statusCode) {
    switch (statusCode) {
      case 401:
        this.handleUnauthorized()
        break
      case 403:
        getApp().showToast('权限不足')
        break
      case 404:
        getApp().showToast('接口不存在')
        break
      case 500:
        getApp().showToast('服务器错误')
        break
      default:
        getApp().showToast(`请求失败 (${statusCode})`)
    }
  }

  // 处理网络错误
  handleNetworkError(error) {
    if (error.errMsg.includes('timeout')) {
      getApp().showToast('请求超时')
    } else if (error.errMsg.includes('fail')) {
      getApp().showToast('网络连接失败')
    } else {
      getApp().showToast('网络错误')
    }
  }

  // 处理未授权
  handleUnauthorized() {
    wx.removeStorageSync('token')
    wx.removeStorageSync('userInfo')
    getApp().showToast('登录已过期')
    
    setTimeout(() => {
      wx.redirectTo({
        url: '/pages/login/login'
      })
    }, 1500)
  }
}

// 创建API实例
const api = new ApiService()

// 具体的API方法
const apiMethods = {
  // 用户相关
  user: {
    login: (data) => api.post('/auth/login', data),
    register: (data) => api.post('/auth/register', data),
    getUserInfo: () => api.get('/user/info'),
    updateUserInfo: (data) => api.put('/user/info', data),
    uploadAvatar: (filePath) => api.uploadFile(filePath, '/user/avatar')
  },

  // 商品相关
  product: {
    getList: (params) => api.get('/products', params),
    getDetail: (id) => api.get(`/products/${id}`),
    search: (keyword, params) => api.get('/products/search', { keyword, ...params }),
    getCategories: () => api.get('/products/categories')
  },

  // 订单相关
  order: {
    getList: (params) => api.get('/orders', params),
    getDetail: (id) => api.get(`/orders/${id}`),
    create: (data) => api.post('/orders', data),
    cancel: (id) => api.put(`/orders/${id}/cancel`),
    pay: (id, data) => api.post(`/orders/${id}/pay`, data),
    confirm: (id) => api.put(`/orders/${id}/confirm`)
  },

  // 团购相关
  group: {
    getList: (params) => api.get('/groups', params),
    getDetail: (id) => api.get(`/groups/${id}`),
    create: (data) => api.post('/groups', data),
    join: (id, data) => api.post(`/groups/${id}/join`, data),
    getTypes: () => api.get('/groups/types')
  },

  // 消息相关
  message: {
    getList: (params) => api.get('/messages', params),
    markRead: (id) => api.put(`/messages/${id}/read`),
    markAllRead: () => api.put('/messages/read-all')
  },

  // 地址相关
  address: {
    getList: () => api.get('/addresses'),
    create: (data) => api.post('/addresses', data),
    update: (id, data) => api.put(`/addresses/${id}`, data),
    delete: (id) => api.delete(`/addresses/${id}`),
    setDefault: (id) => api.put(`/addresses/${id}/default`)
  }
}

module.exports = apiMethods
