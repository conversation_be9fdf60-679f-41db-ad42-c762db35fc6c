/* pages/message/message.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 标签切换 */
.tab-bar {
  display: flex;
  background-color: white;
  border-bottom: 1rpx solid #e5e5e5;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #666666;
  position: relative;
}

.tab-item.active {
  color: #07c160;
  font-weight: bold;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #07c160;
  border-radius: 2rpx;
}

/* 搜索栏 */
.search-bar {
  background-color: white;
  padding: 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.search-input {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 50rpx;
  padding: 15rpx 20rpx;
}

.search-icon {
  margin-right: 10rpx;
}

.search-field {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}

/* 功能图标 */
.function-icons {
  display: flex;
  justify-content: space-around;
  background-color: white;
  padding: 40rpx 20rpx;
  margin-bottom: 20rpx;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  border-radius: 15rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15rpx;
}

.icon-text {
  font-size: 32rpx;
  color: white;
}

.icon-label {
  font-size: 24rpx;
  color: #666666;
}

/* 推荐关注 */
.recommend-section {
  background-color: white;
  margin-bottom: 20rpx;
}

.section-header {
  display: flex;
  align-items: center;
  padding: 30rpx 20rpx 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  margin-right: 15rpx;
}

.section-subtitle {
  font-size: 22rpx;
  color: #999999;
}

.recommend-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.recommend-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.recommend-info {
  flex: 1;
}

.recommend-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.recommend-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
}

.subscribe-btn {
  background-color: #07c160;
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 8rpx 20rpx;
  font-size: 22rpx;
}

.recommend-subtitle {
  font-size: 24rpx;
  color: #999999;
}

.recommend-product {
  display: flex;
  padding: 20rpx;
  background-color: #f9f9f9;
  margin: 20rpx;
  border-radius: 10rpx;
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
}

.product-info {
  flex: 1;
}

.product-title {
  font-size: 26rpx;
  color: #333333;
  line-height: 1.4;
  margin-bottom: 15rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.product-stats {
  display: flex;
  gap: 20rpx;
  margin-bottom: 15rpx;
}

.product-participants, .product-views {
  font-size: 22rpx;
  color: #999999;
}

.product-actions {
  display: flex;
  gap: 15rpx;
}

.share-btn, .buy-btn {
  border: none;
  border-radius: 20rpx;
  padding: 8rpx 20rpx;
  font-size: 22rpx;
}

.share-btn {
  background-color: #f5f5f5;
  color: #666666;
}

.buy-btn {
  background-color: #07c160;
  color: white;
}

/* 消息列表 */
.message-list {
  padding: 0 20rpx;
}

.message-item {
  display: flex;
  align-items: flex-start;
  background-color: white;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  position: relative;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.message-item.unread {
  background-color: #f0f9ff;
}

.message-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.message-content {
  flex: 1;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.message-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
}

.message-time {
  font-size: 22rpx;
  color: #999999;
}

.message-text {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.4;
}

.unread-dot {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 12rpx;
  height: 12rpx;
  background-color: #fa5151;
  border-radius: 50%;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 20rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  color: #999999;
  font-size: 28rpx;
}
