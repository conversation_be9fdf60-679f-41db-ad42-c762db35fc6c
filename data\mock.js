// 模拟数据
const mockData = {
  // 轮播图数据
  banners: [
    {
      id: 1,
      image: 'https://via.placeholder.com/750x300/07c160/ffffff?text=Banner1',
      title: '福利专场-限时低价',
      link: '/pages/category/category?id=1'
    },
    {
      id: 2,
      image: 'https://via.placeholder.com/750x300/ff976a/ffffff?text=Banner2',
      title: '新品上市',
      link: '/pages/category/category?id=2'
    }
  ],

  // 分类数据
  categories: [
    { id: 1, name: '全部', icon: '📦' },
    { id: 2, name: '视频', icon: '📹' },
    { id: 3, name: '最新', icon: '🆕' },
    { id: 4, name: '综合', icon: '🏪' },
    { id: 5, name: '生鲜', icon: '🥬' },
    { id: 6, name: '食品', icon: '🍎' },
    { id: 7, name: '服饰', icon: '👕' }
  ],

  // 商品数据
  products: [
    {
      id: 1,
      title: '【高甜露天黄肉油桃】秒杀4斤🍑桃香浓郁❤️清甜多汁',
      price: 18.8,
      originalPrice: 38.0,
      image: 'https://via.placeholder.com/300x300/ffeb3b/333333?text=桃子',
      images: [
        'https://via.placeholder.com/300x300/ffeb3b/333333?text=桃子1',
        'https://via.placeholder.com/300x300/ffeb3b/333333?text=桃子2',
        'https://via.placeholder.com/300x300/ffeb3b/333333?text=桃子3'
      ],
      sales: 204,
      likes: 592,
      groupType: '现货现发',
      groupPrice: 38,
      groupUnit: '5斤',
      groupLimit: 15,
      description: '新鲜采摘，香甜可口，营养丰富',
      publishTime: Date.now() - 12 * 60 * 1000,
      category: '生鲜',
      specs: [
        { name: '规格', options: ['3斤装', '5斤装', '10斤装'] },
        { name: '包装', options: ['普通包装', '礼盒包装'] }
      ],
      detail: {
        description: '这是一款优质的黄肉油桃，产自山东优质产区，果肉香甜，汁水丰富。',
        images: [
          'https://via.placeholder.com/600x400/ffeb3b/333333?text=详情图1',
          'https://via.placeholder.com/600x400/ffeb3b/333333?text=详情图2'
        ]
      }
    },
    {
      id: 2,
      title: '🔥限时团🔥29.9元2瓶！【ESKE.泰国脚气喷剂】一喷终结脚气',
      price: 29.9,
      originalPrice: 59.8,
      image: 'https://via.placeholder.com/300x300/2196f3/ffffff?text=喷剂',
      images: [
        'https://via.placeholder.com/300x300/2196f3/ffffff?text=喷剂1',
        'https://via.placeholder.com/300x300/2196f3/ffffff?text=喷剂2'
      ],
      sales: 919,
      likes: 1277,
      groupType: '限时团',
      groupPrice: 29.9,
      groupUnit: '2瓶',
      groupLimit: 50,
      description: '泰国进口，快速止痒，效果显著',
      publishTime: Date.now() - 30 * 60 * 1000,
      category: '日用',
      specs: [
        { name: '数量', options: ['1瓶', '2瓶', '3瓶'] }
      ],
      detail: {
        description: '泰国原装进口脚气喷剂，天然植物提取，安全有效。',
        images: [
          'https://via.placeholder.com/600x400/2196f3/ffffff?text=详情图1',
          'https://via.placeholder.com/600x400/2196f3/ffffff?text=详情图2'
        ]
      }
    }
  ],

  // 订单数据
  orders: [
    {
      id: 'O202406210001',
      status: 'pending', // pending, paid, shipped, completed, cancelled
      statusText: '待支付',
      products: [
        {
          id: 1,
          title: '【高甜露天黄肉油桃】秒杀4斤',
          image: 'https://via.placeholder.com/100x100/ffeb3b/333333?text=桃子',
          price: 18.8,
          quantity: 1,
          specs: '5斤装 普通包装'
        }
      ],
      totalPrice: 18.8,
      createTime: Date.now() - 10 * 60 * 1000,
      payTime: null,
      shipTime: null,
      completeTime: null
    }
  ],

  // 消息数据
  messages: [
    {
      id: 1,
      type: 'system',
      title: '考拉优选【订阅领3元红包】',
      content: '今日红包已达达，点击领取下单享...',
      time: Date.now() - 6 * 60 * 60 * 1000,
      read: false,
      avatar: 'https://via.placeholder.com/60x60/07c160/ffffff?text=考拉'
    },
    {
      id: 2,
      type: 'group',
      title: '图图妈高品质团购【订阅领3元红包】',
      content: '超多团头等',
      time: Date.now() - 2 * 60 * 60 * 1000,
      read: false,
      avatar: 'https://via.placeholder.com/60x60/ff976a/ffffff?text=图图'
    }
  ],

  // 团购类型
  groupTypes: [
    {
      id: 1,
      name: '普通团购',
      description: '创建团购商品',
      icon: '🛍️',
      color: '#07c160'
    },
    {
      id: 2,
      name: '预售团购',
      description: '下单预定，承诺时间发货',
      icon: '📦',
      color: '#ff976a'
    },
    {
      id: 3,
      name: '卡券团购',
      description: '下单发码，核销兑换服务',
      icon: '🎫',
      color: '#ff6b6b'
    },
    {
      id: 4,
      name: '报名团购',
      description: '活动报名统计',
      icon: '📝',
      color: '#4ecdc4'
    }
  ],

  // 用户信息
  userInfo: {
    id: 1,
    nickname: '团长小助手',
    avatar: 'https://via.placeholder.com/100x100/07c160/ffffff?text=头像',
    phone: '138****8888',
    isGroupLeader: true,
    groupCount: 25,
    memberCount: 1580
  }
}

module.exports = mockData
