// pages/login/login.js
const util = require('../../utils/util.js')

Page({
  data: {
    phone: '',
    code: '',
    agreedToTerms: false,
    canSendCode: false,
    canLogin: false,
    codeText: '获取验证码',
    countdown: 0
  },

  onLoad() {
    // 检查是否已登录
    const token = wx.getStorageSync('token')
    if (token) {
      this.redirectToHome()
    }
  },

  // 手机号输入
  onPhoneInput(e) {
    const phone = e.detail.value
    this.setData({
      phone: phone,
      canSendCode: util.validatePhone(phone)
    })
    this.checkCanLogin()
  },

  // 验证码输入
  onCodeInput(e) {
    const code = e.detail.value
    this.setData({
      code: code
    })
    this.checkCanLogin()
  },

  // 检查是否可以登录
  checkCanLogin() {
    const { phone, code, agreedToTerms } = this.data
    const canLogin = util.validatePhone(phone) && code.length === 6 && agreedToTerms
    this.setData({
      canLogin: canLogin
    })
  },

  // 发送验证码
  sendCode() {
    if (!this.data.canSendCode || this.data.countdown > 0) {
      return
    }

    const phone = this.data.phone
    
    // 这里应该调用发送验证码的API
    wx.showLoading({
      title: '发送中...'
    })

    // 模拟API调用
    setTimeout(() => {
      wx.hideLoading()
      getApp().showToast('验证码已发送', 'success')
      this.startCountdown()
    }, 1000)
  },

  // 开始倒计时
  startCountdown() {
    let countdown = 60
    this.setData({
      countdown: countdown,
      codeText: `${countdown}s后重发`
    })

    const timer = setInterval(() => {
      countdown--
      if (countdown <= 0) {
        clearInterval(timer)
        this.setData({
          countdown: 0,
          codeText: '获取验证码'
        })
      } else {
        this.setData({
          countdown: countdown,
          codeText: `${countdown}s后重发`
        })
      }
    }, 1000)
  },

  // 同意协议
  onAgreementChange(e) {
    this.setData({
      agreedToTerms: e.detail.value
    })
    this.checkCanLogin()
  },

  // 登录
  login() {
    if (!this.data.canLogin) {
      return
    }

    const { phone, code } = this.data

    wx.showLoading({
      title: '登录中...'
    })

    // 模拟登录API调用
    setTimeout(() => {
      wx.hideLoading()
      
      // 模拟登录成功
      const token = 'mock_token_' + Date.now()
      const userInfo = {
        id: 1,
        nickname: '用户' + phone.substr(-4),
        avatar: 'https://via.placeholder.com/100x100/07c160/ffffff?text=头像',
        phone: phone,
        isGroupLeader: false
      }

      // 保存登录信息
      wx.setStorageSync('token', token)
      wx.setStorageSync('userInfo', userInfo)

      getApp().showToast('登录成功', 'success')
      
      setTimeout(() => {
        this.redirectToHome()
      }, 1500)
    }, 1500)
  },

  // 微信登录
  onWechatLogin(e) {
    if (!this.data.agreedToTerms) {
      getApp().showToast('请先同意用户协议')
      return
    }

    const userInfo = e.detail.userInfo
    if (!userInfo) {
      getApp().showToast('授权失败')
      return
    }

    wx.showLoading({
      title: '登录中...'
    })

    // 获取微信登录code
    wx.login({
      success: (res) => {
        const code = res.code
        
        // 这里应该将code和userInfo发送到后端进行登录
        setTimeout(() => {
          wx.hideLoading()
          
          // 模拟登录成功
          const token = 'wechat_token_' + Date.now()
          const mockUserInfo = {
            id: 1,
            nickname: userInfo.nickName,
            avatar: userInfo.avatarUrl,
            phone: '',
            isGroupLeader: false
          }

          wx.setStorageSync('token', token)
          wx.setStorageSync('userInfo', mockUserInfo)

          getApp().showToast('登录成功', 'success')
          
          setTimeout(() => {
            this.redirectToHome()
          }, 1500)
        }, 1500)
      },
      fail: () => {
        wx.hideLoading()
        getApp().showToast('微信登录失败')
      }
    })
  },

  // 查看用户协议
  viewTerms() {
    wx.navigateTo({
      url: '/pages/terms/terms'
    })
  },

  // 查看隐私政策
  viewPrivacy() {
    wx.navigateTo({
      url: '/pages/privacy/privacy'
    })
  },

  // 跳转到首页
  redirectToHome() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  }
})
