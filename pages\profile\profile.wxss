/* pages/profile/profile.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

/* 用户信息卡片 */
.user-card {
  background-color: white;
  margin: 20rpx;
  border-radius: 15rpx;
  padding: 40rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.user-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 30rpx;
  border: 4rpx solid #f5f5f5;
}

.user-details {
  flex: 1;
}

.user-name {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
}

.user-phone {
  display: block;
  font-size: 26rpx;
  color: #999999;
  margin-bottom: 15rpx;
}

.user-stats {
  display: flex;
  gap: 30rpx;
}

.stat-item {
  font-size: 24rpx;
  color: #07c160;
  font-weight: bold;
}

.user-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.edit-btn {
  background-color: #07c160;
  color: white;
  border: none;
  border-radius: 25rpx;
  padding: 15rpx 30rpx;
  font-size: 24rpx;
}

/* 功能菜单 */
.menu-section {
  padding: 0 20rpx;
}

.menu-group {
  background-color: white;
  border-radius: 15rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
  position: relative;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  margin-right: 25rpx;
}

.menu-text {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}

.menu-badge {
  background-color: #fa5151;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 15rpx;
  margin-right: 15rpx;
  min-width: 30rpx;
  text-align: center;
}

.menu-arrow {
  font-size: 24rpx;
  color: #ccc;
  font-weight: bold;
}

/* 退出登录 */
.logout-section {
  padding: 0 20rpx;
  margin-top: 40rpx;
}

.logout-btn {
  width: 100%;
  background-color: white;
  color: #fa5151;
  border: 1rpx solid #fa5151;
  border-radius: 15rpx;
  padding: 30rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.logout-btn:active {
  background-color: #fef2f2;
}
