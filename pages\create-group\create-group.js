// pages/create-group/create-group.js
const mockData = require('../../data/mock.js')

Page({
  data: {
    groupTypes: [],
    selectedGroupType: {},
    showGroupTypeModal: false
  },

  onLoad() {
    this.loadData()
  },

  // 加载数据
  loadData() {
    // 扩展团购类型数据，添加更多详细信息
    const groupTypes = mockData.groupTypes.map(type => ({
      ...type,
      features: this.getGroupTypeFeatures(type.id),
      example: this.getGroupTypeExample(type.id)
    }))

    this.setData({
      groupTypes: groupTypes
    })
  },

  // 获取团购类型特点
  getGroupTypeFeatures(typeId) {
    const featuresMap = {
      1: [
        '支持多规格商品',
        '自动统计订单',
        '支持在线支付',
        '一键生成分享海报'
      ],
      2: [
        '支持预售模式',
        '可设置发货时间',
        '支持定金+尾款',
        '自动提醒发货'
      ],
      3: [
        '电子卡券发放',
        '支持核销功能',
        '可设置使用期限',
        '防伪验证'
      ],
      4: [
        '活动报名统计',
        '支持报名费收取',
        '参与人员管理',
        '活动提醒通知'
      ]
    }
    return featuresMap[typeId] || []
  },

  // 获取团购类型使用场景
  getGroupTypeExample(typeId) {
    const exampleMap = {
      1: '适用于水果生鲜、日用百货、服装鞋帽等实物商品团购',
      2: '适用于季节性商品、定制商品、大件商品等需要预售的场景',
      3: '适用于餐饮券、服务券、会员卡等虚拟商品',
      4: '适用于亲子活动、培训课程、聚会聚餐等活动报名'
    }
    return exampleMap[typeId] || ''
  },

  // 选择团购类型
  selectGroupType(e) {
    const groupType = e.currentTarget.dataset.type
    this.setData({
      selectedGroupType: groupType,
      showGroupTypeModal: true
    })
  },

  // 隐藏团购类型弹窗
  hideGroupTypeModal() {
    this.setData({
      showGroupTypeModal: false
    })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 空函数，用于阻止事件冒泡
  },

  // 确认选择团购类型
  confirmGroupType() {
    const groupType = this.data.selectedGroupType
    
    this.hideGroupTypeModal()
    
    // 根据不同的团购类型跳转到不同的创建页面
    let url = ''
    switch (groupType.id) {
      case 1:
        url = '/pages/create-normal-group/create-normal-group'
        break
      case 2:
        url = '/pages/create-presale-group/create-presale-group'
        break
      case 3:
        url = '/pages/create-coupon-group/create-coupon-group'
        break
      case 4:
        url = '/pages/create-signup-group/create-signup-group'
        break
      default:
        getApp().showToast('功能开发中')
        return
    }

    wx.navigateTo({
      url: url
    })
  },

  // 选择用户类型
  selectUserType(e) {
    const userType = e.currentTarget.dataset.type
    
    if (userType === 'newbie') {
      // 新手引导
      wx.showModal({
        title: '新手引导',
        content: '我们将为您提供详细的开团教程，帮助您快速上手。',
        confirmText: '开始学习',
        cancelText: '稍后再说',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/tutorial/tutorial'
            })
          }
        }
      })
    } else {
      // 有经验用户直接选择团购类型
      wx.showToast({
        title: '请选择上方团购类型',
        icon: 'none',
        duration: 2000
      })
    }
  },

  // 查看规则
  viewRules() {
    wx.navigateTo({
      url: '/pages/rules/rules'
    })
  },

  // 设置提醒
  onSettingTap() {
    wx.showModal({
      title: '开启通知',
      content: '开启后可以及时收到官方活动和优惠信息',
      confirmText: '去设置',
      success: (res) => {
        if (res.confirm) {
          wx.openSetting({
            success: (settingRes) => {
              if (settingRes.authSetting['scope.userInfo']) {
                getApp().showToast('设置成功', 'success')
              }
            }
          })
        }
      }
    })
  }
})
