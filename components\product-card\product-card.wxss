/* components/product-card/product-card.wxss */
.product-card {
  background-color: white;
  border-radius: 10rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.product-image-container {
  position: relative;
  height: 400rpx;
}

.product-image {
  width: 100%;
  height: 100%;
}

.product-tag {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  background-color: rgba(255, 107, 107, 0.9);
  color: white;
  padding: 5rpx 15rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
}

.product-info {
  padding: 20rpx;
}

.product-title {
  font-size: 28rpx;
  font-weight: bold;
  line-height: 1.4;
  margin-bottom: 15rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.product-price {
  margin-bottom: 15rpx;
}

.current-price {
  color: #fa5151;
  font-size: 32rpx;
  font-weight: bold;
  margin-right: 10rpx;
}

.original-price {
  color: #999999;
  font-size: 24rpx;
  text-decoration: line-through;
}

.product-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stats-text {
  color: #666666;
  font-size: 24rpx;
}

.publish-time {
  color: #999999;
  font-size: 22rpx;
}
