<!--pages/create-group/create-group.wxml-->
<view class="container">
  <!-- 顶部提示 -->
  <view class="top-notice">
    <text class="notice-text">打开提醒，官方活动早知道</text>
    <button class="setting-btn" size="mini">设置</button>
  </view>

  <!-- 插画区域 -->
  <view class="illustration">
    <image class="illustration-image" src="/images/create-group-banner.png" mode="aspectFit"></image>
  </view>

  <!-- 选择开团类型标题 -->
  <view class="section-title">
    <text class="title-text">请选择开团类型</text>
    <view class="title-tabs">
      <text class="tab-item active">开团教程</text>
      <text class="tab-item">新人攻略</text>
    </view>
  </view>

  <!-- 开团类型列表 -->
  <view class="group-types">
    <view class="group-type-item" wx:for="{{groupTypes}}" wx:key="id" bindtap="selectGroupType" data-type="{{item}}">
      <view class="type-icon" style="background-color: {{item.color}}">
        <text class="icon-text">{{item.icon}}</text>
      </view>
      <view class="type-info">
        <text class="type-name">{{item.name}}</text>
        <text class="type-description">{{item.description}}</text>
      </view>
      <view class="type-arrow">
        <icon type="info" size="16" color="#ccc"></icon>
      </view>
    </view>
  </view>

  <!-- 请选择标题 -->
  <view class="select-title">
    <text class="select-text">请选择</text>
  </view>

  <!-- 用户类型选择 -->
  <view class="user-types">
    <view class="user-type-item" bindtap="selectUserType" data-type="newbie">
      <view class="user-type-icon">
        <text class="user-icon">🚀</text>
      </view>
      <view class="user-type-info">
        <text class="user-type-name">我是新手，体验开团</text>
      </view>
      <view class="user-type-arrow">
        <icon type="info" size="16" color="#ccc"></icon>
      </view>
    </view>
    
    <view class="user-type-item" bindtap="selectUserType" data-type="experienced">
      <view class="user-type-icon">
        <text class="user-icon">📦</text>
      </view>
      <view class="user-type-info">
        <text class="user-type-name">我有经验，自己开团</text>
      </view>
      <view class="user-type-arrow">
        <icon type="info" size="16" color="#ccc"></icon>
      </view>
    </view>
  </view>

  <!-- 底部规则链接 -->
  <view class="bottom-rules">
    <text class="rules-link" bindtap="viewRules">《快团团禁发商品及信息管理规范》</text>
  </view>
</view>

<!-- 开团类型选择弹窗 -->
<view class="group-type-modal {{showGroupTypeModal ? 'show' : ''}}" bindtap="hideGroupTypeModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">{{selectedGroupType.name}}</text>
      <view class="modal-close" bindtap="hideGroupTypeModal">×</view>
    </view>
    
    <view class="modal-body">
      <view class="modal-description">{{selectedGroupType.description}}</view>
      
      <view class="modal-features" wx:if="{{selectedGroupType.features}}">
        <view class="feature-item" wx:for="{{selectedGroupType.features}}" wx:key="*this">
          <text class="feature-text">• {{item}}</text>
        </view>
      </view>
      
      <view class="modal-example" wx:if="{{selectedGroupType.example}}">
        <text class="example-title">使用场景：</text>
        <text class="example-text">{{selectedGroupType.example}}</text>
      </view>
    </view>
    
    <view class="modal-footer">
      <button class="modal-btn cancel" bindtap="hideGroupTypeModal">取消</button>
      <button class="modal-btn confirm" bindtap="confirmGroupType">确定选择</button>
    </view>
  </view>
</view>
