// 小程序配置文件
const config = {
  // 环境配置
  env: 'development', // development, production
  
  // API配置
  api: {
    baseUrl: 'https://api.kuaituantuan.com',
    timeout: 10000,
    version: 'v1'
  },
  
  // 微信支付配置
  payment: {
    mchId: 'your_mch_id',
    api<PERSON>ey: 'your_api_key'
  },
  
  // 分页配置
  pagination: {
    pageSize: 10,
    maxPageSize: 50
  },
  
  // 图片配置
  image: {
    maxSize: 5 * 1024 * 1024, // 5MB
    allowedTypes: ['jpg', 'jpeg', 'png', 'gif'],
    compressQuality: 0.8
  },
  
  // 缓存配置
  cache: {
    userInfoExpire: 7 * 24 * 60 * 60 * 1000, // 7天
    tokenExpire: 30 * 24 * 60 * 60 * 1000, // 30天
    searchHistoryMax: 20
  },
  
  // 业务配置
  business: {
    maxCartItems: 99,
    maxAddressCount: 10,
    minOrderAmount: 0.01,
    maxOrderAmount: 99999.99
  },
  
  // 界面配置
  ui: {
    primaryColor: '#07c160',
    dangerColor: '#fa5151',
    warningColor: '#ff976a',
    infoColor: '#1890ff',
    successColor: '#52c41a',
    
    // 动画时长
    animationDuration: {
      fast: 200,
      normal: 300,
      slow: 500
    }
  },
  
  // 分享配置
  share: {
    title: '快团团 - 优质团购平台',
    desc: '发现更多优质商品，一键开团',
    imageUrl: '/images/share-logo.png'
  },
  
  // 错误码配置
  errorCodes: {
    SUCCESS: 200,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    SERVER_ERROR: 500,
    NETWORK_ERROR: -1,
    TIMEOUT: -2
  },
  
  // 正则表达式
  regex: {
    phone: /^1[3-9]\d{9}$/,
    email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    idCard: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
    password: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/
  }
}

// 根据环境切换配置
if (config.env === 'production') {
  config.api.baseUrl = 'https://api.kuaituantuan.com'
} else {
  config.api.baseUrl = 'https://dev-api.kuaituantuan.com'
}

module.exports = config
