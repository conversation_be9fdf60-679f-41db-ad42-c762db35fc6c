<!--pages/index/index.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar" bindtap="goToSearch">
    <view class="search-input">
      <icon class="search-icon" type="search" size="16" color="#999"></icon>
      <text class="search-placeholder">天润浓缩牛奶</text>
    </view>
    <view class="message-icon" bindtap="goToMessage">
      <icon type="info" size="20" color="#333"></icon>
    </view>
  </view>

  <!-- 轮播图 -->
  <view class="banner-section">
    <swiper class="banner-swiper" indicator-dots="{{true}}" autoplay="{{true}}" interval="3000" duration="500">
      <swiper-item wx:for="{{banners}}" wx:key="id">
        <image class="banner-image" src="{{item.image}}" mode="aspectFill" bindtap="onBannerTap" data-link="{{item.link}}"></image>
      </swiper-item>
    </swiper>
  </view>

  <!-- 分类导航 -->
  <view class="category-nav">
    <scroll-view class="category-scroll" scroll-x="true">
      <view class="category-list">
        <view class="category-item {{currentCategory === item.id ? 'active' : ''}}" 
              wx:for="{{categories}}" wx:key="id" 
              bindtap="onCategoryTap" data-id="{{item.id}}">
          <text class="category-icon">{{item.icon}}</text>
          <text class="category-name">{{item.name}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 商品列表 -->
  <view class="product-list">
    <view class="product-item" wx:for="{{products}}" wx:key="id" bindtap="goToProduct" data-id="{{item.id}}">
      <!-- 商品图片 -->
      <view class="product-image-container">
        <image class="product-image" src="{{item.image}}" mode="aspectFill"></image>
        <view class="product-tag">{{item.groupType}}</view>
      </view>
      
      <!-- 商品信息 -->
      <view class="product-info">
        <view class="product-title">{{item.title}}</view>
        <view class="product-price">
          <text class="current-price">¥{{item.price}}</text>
          <text class="original-price" wx:if="{{item.originalPrice}}">¥{{item.originalPrice}}</text>
          <text class="group-info">{{item.groupPrice}}元{{item.groupUnit}}（{{item.groupLimit}}份内）</text>
        </view>
        <view class="product-stats">
          <view class="stats-left">
            <image class="avatar" wx:for="{{item.buyers || []}}" wx:key="*this" src="{{item}}"></image>
            <text class="stats-text">{{item.sales}}人团购 {{item.likes}}人看过</text>
          </view>
          <view class="stats-right">
            <text class="publish-time">{{item.publishTimeText}}</text>
          </view>
        </view>
        <view class="product-actions">
          <button class="share-btn" size="mini" bindtap="onShareProduct" data-id="{{item.id}}" catchtap="true">分享</button>
          <button class="buy-btn" size="mini" bindtap="onBuyProduct" data-id="{{item.id}}" catchtap="true">去购买</button>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore}}">
    <text class="load-text">{{loading ? '加载中...' : '上拉加载更多'}}</text>
  </view>
  
  <!-- 没有更多数据 -->
  <view class="no-more" wx:if="{{!hasMore && products.length > 0}}">
    <text class="no-more-text">没有更多商品了</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{products.length === 0 && !loading}}">
    <image class="empty-image" src="/images/empty-product.png"></image>
    <text class="empty-text">暂无商品</text>
  </view>
</view>
