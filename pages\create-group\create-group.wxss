/* pages/create-group/create-group.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 顶部提示 */
.top-notice {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff3cd;
  padding: 20rpx;
  border-bottom: 1rpx solid #ffeaa7;
}

.notice-text {
  color: #856404;
  font-size: 26rpx;
}

.setting-btn {
  background-color: #07c160;
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 10rpx 20rpx;
  font-size: 22rpx;
}

/* 插画区域 */
.illustration {
  background-color: white;
  padding: 60rpx 40rpx;
  text-align: center;
}

.illustration-image {
  width: 400rpx;
  height: 300rpx;
}

/* 选择开团类型标题 */
.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 40rpx 20rpx;
  background-color: white;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.title-tabs {
  display: flex;
  gap: 20rpx;
}

.tab-item {
  font-size: 24rpx;
  color: #999999;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
}

.tab-item.active {
  background-color: #f0f0f0;
  color: #333333;
}

/* 开团类型列表 */
.group-types {
  background-color: white;
  padding: 0 40rpx 40rpx;
}

.group-type-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.group-type-item:last-child {
  border-bottom: none;
}

.type-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 15rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 30rpx;
}

.icon-text {
  font-size: 36rpx;
}

.type-info {
  flex: 1;
}

.type-name {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
}

.type-description {
  font-size: 24rpx;
  color: #999999;
}

.type-arrow {
  padding: 10rpx;
}

/* 请选择标题 */
.select-title {
  padding: 40rpx;
  text-align: center;
}

.select-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

/* 用户类型选择 */
.user-types {
  padding: 0 40rpx;
}

.user-type-item {
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 15rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.user-type-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 15rpx;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 30rpx;
}

.user-icon {
  font-size: 36rpx;
}

.user-type-info {
  flex: 1;
}

.user-type-name {
  font-size: 30rpx;
  color: #333333;
}

.user-type-arrow {
  padding: 10rpx;
}

/* 底部规则链接 */
.bottom-rules {
  padding: 60rpx 40rpx;
  text-align: center;
}

.rules-link {
  color: #07c160;
  font-size: 24rpx;
  text-decoration: underline;
}

/* 开团类型选择弹窗 */
.group-type-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.group-type-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background-color: white;
  border-radius: 20rpx;
  width: 80%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  transform: scale(0.8);
  transition: transform 0.3s ease;
}

.group-type-modal.show .modal-content {
  transform: scale(1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.modal-close {
  font-size: 40rpx;
  color: #999999;
  padding: 10rpx;
  line-height: 1;
}

.modal-body {
  padding: 40rpx;
  max-height: 400rpx;
  overflow-y: auto;
}

.modal-description {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
  margin-bottom: 30rpx;
}

.modal-features {
  margin-bottom: 30rpx;
}

.feature-item {
  margin-bottom: 15rpx;
}

.feature-text {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
}

.modal-example {
  background-color: #f9f9f9;
  padding: 20rpx;
  border-radius: 10rpx;
}

.example-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
}

.example-text {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.5;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #f5f5f5;
}

.modal-btn {
  flex: 1;
  padding: 30rpx;
  font-size: 28rpx;
  border: none;
  background-color: white;
}

.modal-btn.cancel {
  color: #666666;
  border-right: 1rpx solid #f5f5f5;
}

.modal-btn.confirm {
  color: #07c160;
  font-weight: bold;
}
