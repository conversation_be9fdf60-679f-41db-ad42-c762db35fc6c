/* pages/login/login.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 100rpx 60rpx 60rpx;
  display: flex;
  flex-direction: column;
}

/* Logo区域 */
.logo-section {
  text-align: center;
  margin-bottom: 100rpx;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
}

.app-name {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 15rpx;
}

.app-slogan {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 登录表单 */
.login-form {
  flex: 1;
}

.input-group {
  margin-bottom: 60rpx;
}

.input-item {
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 50rpx;
  padding: 25rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.input-icon {
  margin-right: 20rpx;
}

.input-field {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}

.code-btn {
  background-color: #07c160;
  color: white;
  border: none;
  border-radius: 25rpx;
  padding: 10rpx 25rpx;
  font-size: 24rpx;
  margin-left: 20rpx;
}

.code-btn.disabled {
  background-color: #cccccc;
  color: #999999;
}

.login-btn {
  width: 100%;
  background-color: rgba(255, 255, 255, 0.3);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  border-radius: 50rpx;
  padding: 25rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 40rpx;
}

.login-btn.active {
  background-color: white;
  color: #667eea;
  border-color: white;
}

.agreement {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 60rpx;
}

.agreement-checkbox {
  margin-right: 10rpx;
}

.agreement-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.agreement-link {
  font-size: 24rpx;
  color: white;
  text-decoration: underline;
}

/* 微信登录 */
.wechat-login {
  margin-bottom: 60rpx;
}

.divider {
  text-align: center;
  margin-bottom: 40rpx;
  position: relative;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1rpx;
  background-color: rgba(255, 255, 255, 0.3);
}

.divider-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0 30rpx;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.wechat-btn {
  width: 100%;
  background-color: white;
  color: #07c160;
  border: none;
  border-radius: 50rpx;
  padding: 25rpx;
  font-size: 28rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.wechat-btn icon {
  margin-right: 15rpx;
}

/* 底部提示 */
.bottom-tips {
  text-align: center;
}

.tips-text {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1.5;
}
