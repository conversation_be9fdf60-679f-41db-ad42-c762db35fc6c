<!--pages/order/order.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input">
      <icon class="search-icon" type="search" size="16" color="#999"></icon>
      <input class="search-field" placeholder="搜索商品名" value="{{searchKeyword}}" bindinput="onSearchInput" />
    </view>
  </view>

  <!-- 状态标签 -->
  <view class="status-tabs">
    <view class="tab-item {{currentStatus === item.key ? 'active' : ''}}" 
          wx:for="{{statusTabs}}" wx:key="key"
          bindtap="onStatusTap" data-status="{{item.key}}">
      {{item.name}}
    </view>
  </view>

  <!-- 团长推荐 -->
  <view class="leader-recommend" wx:if="{{currentStatus === 'all'}}">
    <view class="recommend-header">
      <text class="recommend-title">🔥团长热卖好货</text>
      <text class="recommend-subtitle">(仅展示已关注团长的团购)</text>
      <text class="recommend-more">更多 ></text>
    </view>
    
    <scroll-view class="recommend-scroll" scroll-x="true">
      <view class="recommend-list">
        <view class="recommend-item" wx:for="{{recommendProducts}}" wx:key="id" bindtap="goToProduct" data-id="{{item.id}}">
          <image class="recommend-image" src="{{item.image}}" mode="aspectFill"></image>
          <view class="recommend-info">
            <text class="recommend-price">¥{{item.price}}</text>
            <text class="recommend-original-price">¥{{item.originalPrice}}</text>
          </view>
          <view class="recommend-tag">{{item.tag}}</view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 订单列表 -->
  <view class="order-list">
    <view class="order-item" wx:for="{{orders}}" wx:key="id">
      <view class="order-header">
        <text class="order-id">订单号：{{item.id}}</text>
        <text class="order-status {{item.status}}">{{item.statusText}}</text>
      </view>
      
      <view class="order-products">
        <view class="product-item" wx:for="{{item.products}}" wx:key="id" wx:for-item="product">
          <image class="product-image" src="{{product.image}}" mode="aspectFill" bindtap="goToProduct" data-id="{{product.id}}"></image>
          <view class="product-info">
            <view class="product-title">{{product.title}}</view>
            <view class="product-specs" wx:if="{{product.specs}}">{{product.specs}}</view>
            <view class="product-price-quantity">
              <text class="product-price">¥{{product.price}}</text>
              <text class="product-quantity">x{{product.quantity}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <view class="order-footer">
        <view class="order-total">
          <text class="total-label">共{{item.products.length}}件商品 合计：</text>
          <text class="total-price">¥{{item.totalPrice}}</text>
        </view>
        <view class="order-actions">
          <button class="action-btn secondary" wx:if="{{item.status === 'pending'}}" bindtap="cancelOrder" data-id="{{item.id}}">取消订单</button>
          <button class="action-btn primary" wx:if="{{item.status === 'pending'}}" bindtap="payOrder" data-id="{{item.id}}">立即支付</button>
          <button class="action-btn secondary" wx:if="{{item.status === 'shipped'}}" bindtap="confirmReceive" data-id="{{item.id}}">确认收货</button>
          <button class="action-btn secondary" bindtap="viewOrderDetail" data-id="{{item.id}}">查看详情</button>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{orders.length === 0 && !loading}}">
    <image class="empty-image" src="/images/empty-order.png"></image>
    <text class="empty-text">{{emptyText}}</text>
    <button class="empty-btn" bindtap="goToHome">去逛逛</button>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <text class="loading-text">加载中...</text>
  </view>
</view>
