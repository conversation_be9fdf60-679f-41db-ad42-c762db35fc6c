// app.js
const config = require('./config/config.js')
const store = require('./store/index.js')
const api = require('./services/api.js')

App({
  onLaunch() {
    // 初始化应用
    this.initApp()

    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 检查更新
    store.checkUpdate()

    // 登录
    this.autoLogin()
  },

  onShow() {
    // 应用显示时的处理
    console.log('App Show')
  },

  onHide() {
    // 应用隐藏时的处理
    console.log('App Hide')
  },

  onError(error) {
    // 应用错误处理
    console.error('App Error:', error)
  },

  globalData: {
    userInfo: null,
    baseUrl: config.api.baseUrl,
    version: config.ui.version || '1.0.0',
    store: store,
    api: api,
    config: config
  },

  // 初始化应用
  initApp() {
    // 设置全局store引用
    this.globalData.store = store

    // 监听网络状态
    this.watchNetworkStatus()

    // 设置导航栏
    this.setNavigationBar()
  },

  // 自动登录
  autoLogin() {
    const token = wx.getStorageSync('token')
    if (token) {
      // 验证token有效性
      api.user.getUserInfo().then(userInfo => {
        store.setUserInfo(userInfo)
      }).catch(() => {
        // token无效，清除本地数据
        store.clearUserInfo()
      })
    } else {
      // 静默登录
      wx.login({
        success: res => {
          console.log('微信登录成功', res.code)
          // 这里可以将code发送到后端进行静默登录
        },
        fail: err => {
          console.error('微信登录失败', err)
        }
      })
    }
  },

  // 监听网络状态
  watchNetworkStatus() {
    wx.onNetworkStatusChange((res) => {
      if (!res.isConnected) {
        this.showToast('网络连接已断开', 'none')
      } else if (store.getState('networkType') === 'none') {
        this.showToast('网络连接已恢复', 'success')
      }
    })
  },

  // 设置导航栏
  setNavigationBar() {
    wx.setNavigationBarColor({
      frontColor: '#000000',
      backgroundColor: '#ffffff'
    })
  },

  // 全局方法
  request(options) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: this.globalData.baseUrl + options.url,
        method: options.method || 'GET',
        data: options.data || {},
        header: {
          'content-type': 'application/json',
          'Authorization': wx.getStorageSync('token') || '',
          ...options.header
        },
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data)
          } else {
            reject(res)
          }
        },
        fail: reject
      })
    })
  },

  // 显示加载提示
  showLoading(title = '加载中...') {
    wx.showLoading({
      title: title,
      mask: true
    })
  },

  // 隐藏加载提示
  hideLoading() {
    wx.hideLoading()
  },

  // 显示消息提示
  showToast(title, icon = 'none') {
    wx.showToast({
      title: title,
      icon: icon,
      duration: 2000
    })
  }
})
