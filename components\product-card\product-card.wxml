<!--components/product-card/product-card.wxml-->
<view class="product-card" bindtap="onTap">
  <!-- 商品图片 -->
  <view class="product-image-container">
    <image class="product-image" src="{{product.image}}" mode="aspectFill"></image>
    <view class="product-tag" wx:if="{{product.groupType}}">{{product.groupType}}</view>
  </view>
  
  <!-- 商品信息 -->
  <view class="product-info">
    <view class="product-title">{{product.title}}</view>
    <view class="product-price">
      <text class="current-price">¥{{product.price}}</text>
      <text class="original-price" wx:if="{{product.originalPrice}}">¥{{product.originalPrice}}</text>
    </view>
    <view class="product-stats" wx:if="{{showStats}}">
      <text class="stats-text">{{product.sales}}人团购 {{product.likes}}人看过</text>
      <text class="publish-time">{{product.publishTimeText}}</text>
    </view>
  </view>
</view>
