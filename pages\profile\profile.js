// pages/profile/profile.js
const mockData = require('../../data/mock.js')

Page({
  data: {
    userInfo: {},
    couponCount: 3
  },

  onLoad() {
    this.loadUserInfo()
  },

  onShow() {
    // 页面显示时刷新用户信息
    this.loadUserInfo()
  },

  // 加载用户信息
  loadUserInfo() {
    // 从模拟数据获取用户信息
    this.setData({
      userInfo: mockData.userInfo
    })
  },

  // 更换头像
  changeAvatar() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0]
        
        // 这里应该上传头像到服务器
        wx.showLoading({
          title: '上传中...'
        })
        
        setTimeout(() => {
          wx.hideLoading()
          this.setData({
            'userInfo.avatar': tempFilePath
          })
          getApp().showToast('头像更新成功', 'success')
        }, 1500)
      }
    })
  },

  // 编辑个人资料
  editProfile() {
    wx.navigateTo({
      url: '/pages/edit-profile/edit-profile'
    })
  },

  // 我的订单
  goToOrders() {
    wx.switchTab({
      url: '/pages/order/order'
    })
  },

  // 我的收藏
  goToFavorites() {
    wx.navigateTo({
      url: '/pages/favorites/favorites'
    })
  },

  // 收货地址
  goToAddress() {
    wx.navigateTo({
      url: '/pages/address/address'
    })
  },

  // 我的钱包
  goToWallet() {
    wx.navigateTo({
      url: '/pages/wallet/wallet'
    })
  },

  // 优惠券
  goToCoupons() {
    wx.navigateTo({
      url: '/pages/coupons/coupons'
    })
  },

  // 积分商城
  goToPoints() {
    wx.navigateTo({
      url: '/pages/points/points'
    })
  },

  // 团购管理
  goToGroupManage() {
    wx.navigateTo({
      url: '/pages/group-manage/group-manage'
    })
  },

  // 数据分析
  goToDataAnalysis() {
    wx.navigateTo({
      url: '/pages/data-analysis/data-analysis'
    })
  },

  // 提现管理
  goToWithdraw() {
    wx.navigateTo({
      url: '/pages/withdraw/withdraw'
    })
  },

  // 帮助中心
  goToHelp() {
    wx.navigateTo({
      url: '/pages/help/help'
    })
  },

  // 意见反馈
  goToFeedback() {
    wx.navigateTo({
      url: '/pages/feedback/feedback'
    })
  },

  // 关于我们
  goToAbout() {
    wx.navigateTo({
      url: '/pages/about/about'
    })
  },

  // 设置
  goToSettings() {
    wx.navigateTo({
      url: '/pages/settings/settings'
    })
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除本地存储的用户信息
          wx.removeStorageSync('token')
          wx.removeStorageSync('userInfo')
          
          // 重置用户信息
          this.setData({
            userInfo: {}
          })
          
          getApp().showToast('已退出登录', 'success')
          
          // 跳转到登录页面
          setTimeout(() => {
            wx.redirectTo({
              url: '/pages/login/login'
            })
          }, 1500)
        }
      }
    })
  }
})
