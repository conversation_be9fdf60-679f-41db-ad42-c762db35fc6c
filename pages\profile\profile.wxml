<!--pages/profile/profile.wxml-->
<view class="container">
  <!-- 用户信息卡片 -->
  <view class="user-card">
    <view class="user-info">
      <image class="user-avatar" src="{{userInfo.avatar}}" mode="aspectFill" bindtap="changeAvatar"></image>
      <view class="user-details">
        <text class="user-name">{{userInfo.nickname}}</text>
        <text class="user-phone">{{userInfo.phone}}</text>
        <view class="user-stats" wx:if="{{userInfo.isGroupLeader}}">
          <text class="stat-item">{{userInfo.groupCount}}个团</text>
          <text class="stat-item">{{userInfo.memberCount}}个团员</text>
        </view>
      </view>
    </view>
    <view class="user-actions">
      <button class="edit-btn" size="mini" bindtap="editProfile">编辑</button>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <view class="menu-group">
      <view class="menu-item" bindtap="goToOrders">
        <view class="menu-icon">📦</view>
        <text class="menu-text">我的订单</text>
        <view class="menu-arrow">></view>
      </view>
      <view class="menu-item" bindtap="goToFavorites">
        <view class="menu-icon">❤️</view>
        <text class="menu-text">我的收藏</text>
        <view class="menu-arrow">></view>
      </view>
      <view class="menu-item" bindtap="goToAddress">
        <view class="menu-icon">📍</view>
        <text class="menu-text">收货地址</text>
        <view class="menu-arrow">></view>
      </view>
    </view>

    <view class="menu-group">
      <view class="menu-item" bindtap="goToWallet">
        <view class="menu-icon">💰</view>
        <text class="menu-text">我的钱包</text>
        <view class="menu-arrow">></view>
      </view>
      <view class="menu-item" bindtap="goToCoupons">
        <view class="menu-icon">🎫</view>
        <text class="menu-text">优惠券</text>
        <view class="menu-badge" wx:if="{{couponCount > 0}}">{{couponCount}}</view>
        <view class="menu-arrow">></view>
      </view>
      <view class="menu-item" bindtap="goToPoints">
        <view class="menu-icon">⭐</view>
        <text class="menu-text">积分商城</text>
        <view class="menu-arrow">></view>
      </view>
    </view>

    <view class="menu-group" wx:if="{{userInfo.isGroupLeader}}">
      <view class="menu-item" bindtap="goToGroupManage">
        <view class="menu-icon">👥</view>
        <text class="menu-text">团购管理</text>
        <view class="menu-arrow">></view>
      </view>
      <view class="menu-item" bindtap="goToDataAnalysis">
        <view class="menu-icon">📊</view>
        <text class="menu-text">数据分析</text>
        <view class="menu-arrow">></view>
      </view>
      <view class="menu-item" bindtap="goToWithdraw">
        <view class="menu-icon">💳</view>
        <text class="menu-text">提现管理</text>
        <view class="menu-arrow">></view>
      </view>
    </view>

    <view class="menu-group">
      <view class="menu-item" bindtap="goToHelp">
        <view class="menu-icon">❓</view>
        <text class="menu-text">帮助中心</text>
        <view class="menu-arrow">></view>
      </view>
      <view class="menu-item" bindtap="goToFeedback">
        <view class="menu-icon">💬</view>
        <text class="menu-text">意见反馈</text>
        <view class="menu-arrow">></view>
      </view>
      <view class="menu-item" bindtap="goToAbout">
        <view class="menu-icon">ℹ️</view>
        <text class="menu-text">关于我们</text>
        <view class="menu-arrow">></view>
      </view>
    </view>

    <view class="menu-group">
      <view class="menu-item" bindtap="goToSettings">
        <view class="menu-icon">⚙️</view>
        <text class="menu-text">设置</text>
        <view class="menu-arrow">></view>
      </view>
    </view>
  </view>

  <!-- 退出登录 -->
  <view class="logout-section">
    <button class="logout-btn" bindtap="logout">退出登录</button>
  </view>
</view>
