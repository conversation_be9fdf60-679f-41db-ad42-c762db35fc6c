// 全局状态管理
class Store {
  constructor() {
    this.state = {
      // 用户信息
      userInfo: null,
      isLogin: false,
      
      // 购物车
      cartItems: [],
      cartCount: 0,
      
      // 系统信息
      systemInfo: null,
      
      // 网络状态
      networkType: 'unknown',
      isConnected: true,
      
      // 位置信息
      location: null,
      
      // 应用状态
      appVersion: '1.0.0',
      updateInfo: null
    }
    
    this.listeners = {}
    this.init()
  }

  // 初始化
  init() {
    this.loadUserInfo()
    this.loadCartItems()
    this.getSystemInfo()
    this.watchNetwork()
  }

  // 获取状态
  getState(key) {
    if (key) {
      return this.state[key]
    }
    return this.state
  }

  // 设置状态
  setState(key, value) {
    if (typeof key === 'object') {
      // 批量设置
      Object.keys(key).forEach(k => {
        this.state[k] = key[k]
        this.notify(k, key[k])
      })
    } else {
      // 单个设置
      this.state[key] = value
      this.notify(key, value)
    }
  }

  // 监听状态变化
  subscribe(key, callback) {
    if (!this.listeners[key]) {
      this.listeners[key] = []
    }
    this.listeners[key].push(callback)
    
    // 返回取消订阅的函数
    return () => {
      const index = this.listeners[key].indexOf(callback)
      if (index > -1) {
        this.listeners[key].splice(index, 1)
      }
    }
  }

  // 通知监听者
  notify(key, value) {
    if (this.listeners[key]) {
      this.listeners[key].forEach(callback => {
        callback(value, key)
      })
    }
  }

  // 加载用户信息
  loadUserInfo() {
    const userInfo = wx.getStorageSync('userInfo')
    const token = wx.getStorageSync('token')
    
    if (userInfo && token) {
      this.setState({
        userInfo: userInfo,
        isLogin: true
      })
    }
  }

  // 设置用户信息
  setUserInfo(userInfo) {
    wx.setStorageSync('userInfo', userInfo)
    this.setState({
      userInfo: userInfo,
      isLogin: true
    })
  }

  // 清除用户信息
  clearUserInfo() {
    wx.removeStorageSync('userInfo')
    wx.removeStorageSync('token')
    this.setState({
      userInfo: null,
      isLogin: false
    })
  }

  // 加载购物车
  loadCartItems() {
    const cartItems = wx.getStorageSync('cartItems') || []
    const cartCount = cartItems.reduce((total, item) => total + item.quantity, 0)
    
    this.setState({
      cartItems: cartItems,
      cartCount: cartCount
    })
  }

  // 添加到购物车
  addToCart(product, quantity = 1, specs = {}) {
    const cartItems = [...this.state.cartItems]
    const existingIndex = cartItems.findIndex(item => 
      item.productId === product.id && 
      JSON.stringify(item.specs) === JSON.stringify(specs)
    )

    if (existingIndex > -1) {
      cartItems[existingIndex].quantity += quantity
    } else {
      cartItems.push({
        id: Date.now(),
        productId: product.id,
        title: product.title,
        image: product.image,
        price: product.price,
        quantity: quantity,
        specs: specs,
        selected: true
      })
    }

    this.updateCart(cartItems)
  }

  // 更新购物车
  updateCart(cartItems) {
    const cartCount = cartItems.reduce((total, item) => total + item.quantity, 0)
    
    wx.setStorageSync('cartItems', cartItems)
    this.setState({
      cartItems: cartItems,
      cartCount: cartCount
    })
  }

  // 清空购物车
  clearCart() {
    wx.removeStorageSync('cartItems')
    this.setState({
      cartItems: [],
      cartCount: 0
    })
  }

  // 获取系统信息
  getSystemInfo() {
    wx.getSystemInfo({
      success: (res) => {
        this.setState('systemInfo', res)
      }
    })
  }

  // 监听网络状态
  watchNetwork() {
    wx.getNetworkType({
      success: (res) => {
        this.setState({
          networkType: res.networkType,
          isConnected: res.networkType !== 'none'
        })
      }
    })

    wx.onNetworkStatusChange((res) => {
      this.setState({
        networkType: res.networkType,
        isConnected: res.isConnected
      })
    })
  }

  // 获取位置信息
  getLocation() {
    return new Promise((resolve, reject) => {
      wx.getLocation({
        type: 'gcj02',
        success: (res) => {
          const location = {
            latitude: res.latitude,
            longitude: res.longitude,
            accuracy: res.accuracy
          }
          this.setState('location', location)
          resolve(location)
        },
        fail: reject
      })
    })
  }

  // 检查更新
  checkUpdate() {
    if (wx.getUpdateManager) {
      const updateManager = wx.getUpdateManager()
      
      updateManager.onCheckForUpdate((res) => {
        if (res.hasUpdate) {
          this.setState('updateInfo', {
            hasUpdate: true,
            version: res.version || 'unknown'
          })
        }
      })

      updateManager.onUpdateReady(() => {
        wx.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否重启应用？',
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate()
            }
          }
        })
      })

      updateManager.onUpdateFailed(() => {
        wx.showToast({
          title: '更新失败',
          icon: 'none'
        })
      })
    }
  }
}

// 创建全局store实例
const store = new Store()

module.exports = store
