/* pages/order/order.wxss */
.container {
  background-color: #f5f5f5;
}

/* 搜索栏 */
.search-bar {
  background-color: white;
  padding: 20rpx;
  border-bottom: 1rpx solid #e5e5e5;
}

.search-input {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 50rpx;
  padding: 15rpx 20rpx;
}

.search-icon {
  margin-right: 10rpx;
}

.search-field {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}

/* 状态标签 */
.status-tabs {
  display: flex;
  background-color: white;
  border-bottom: 1rpx solid #e5e5e5;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #666666;
  position: relative;
}

.tab-item.active {
  color: #07c160;
  font-weight: bold;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #07c160;
  border-radius: 2rpx;
}

/* 团长推荐 */
.leader-recommend {
  background-color: white;
  margin-bottom: 20rpx;
}

.recommend-header {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.recommend-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
}

.recommend-subtitle {
  font-size: 22rpx;
  color: #999999;
  margin-left: 10rpx;
}

.recommend-more {
  margin-left: auto;
  font-size: 24rpx;
  color: #07c160;
}

.recommend-scroll {
  white-space: nowrap;
  padding: 20rpx 0;
}

.recommend-list {
  display: inline-flex;
  padding: 0 20rpx;
  gap: 20rpx;
}

.recommend-item {
  position: relative;
  width: 200rpx;
  flex-shrink: 0;
}

.recommend-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 10rpx;
}

.recommend-info {
  padding: 10rpx 0;
}

.recommend-price {
  color: #fa5151;
  font-size: 26rpx;
  font-weight: bold;
  margin-right: 10rpx;
}

.recommend-original-price {
  color: #999999;
  font-size: 22rpx;
  text-decoration: line-through;
}

.recommend-tag {
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  background-color: rgba(255, 107, 107, 0.9);
  color: white;
  padding: 5rpx 10rpx;
  border-radius: 15rpx;
  font-size: 20rpx;
}

/* 订单列表 */
.order-list {
  padding: 0 20rpx;
}

.order-item {
  background-color: white;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.order-id {
  font-size: 26rpx;
  color: #666666;
}

.order-status {
  font-size: 26rpx;
  font-weight: bold;
}

.order-status.pending {
  color: #ff976a;
}

.order-status.paid {
  color: #07c160;
}

.order-status.shipped {
  color: #1890ff;
}

.order-status.completed {
  color: #52c41a;
}

.order-status.cancelled {
  color: #999999;
}

.order-products {
  padding: 20rpx;
}

.product-item {
  display: flex;
  margin-bottom: 20rpx;
}

.product-item:last-child {
  margin-bottom: 0;
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
}

.product-info {
  flex: 1;
}

.product-title {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.4;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.product-specs {
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 10rpx;
}

.product-price-quantity {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-price {
  color: #fa5151;
  font-size: 28rpx;
  font-weight: bold;
}

.product-quantity {
  color: #666666;
  font-size: 26rpx;
}

.order-footer {
  padding: 20rpx;
  border-top: 1rpx solid #f5f5f5;
}

.order-total {
  text-align: right;
  margin-bottom: 20rpx;
}

.total-label {
  font-size: 26rpx;
  color: #666666;
}

.total-price {
  color: #fa5151;
  font-size: 30rpx;
  font-weight: bold;
}

.order-actions {
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
}

.action-btn {
  padding: 15rpx 30rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  border: 1rpx solid #e5e5e5;
}

.action-btn.primary {
  background-color: #07c160;
  color: white;
  border-color: #07c160;
}

.action-btn.secondary {
  background-color: white;
  color: #666666;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 20rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  color: #999999;
  font-size: 28rpx;
  margin-bottom: 40rpx;
}

.empty-btn {
  background-color: #07c160;
  color: white;
  border: none;
  border-radius: 30rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 100rpx 20rpx;
}

.loading-text {
  color: #999999;
  font-size: 28rpx;
}
