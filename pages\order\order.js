// pages/order/order.js
const mockData = require('../../data/mock.js')
const util = require('../../utils/util.js')

Page({
  data: {
    currentStatus: 'all',
    statusTabs: [
      { key: 'all', name: '全部' },
      { key: 'pending', name: '待支付' },
      { key: 'shipped', name: '发货' },
      { key: 'completed', name: '售后' }
    ],
    searchKeyword: '',
    orders: [],
    recommendProducts: [],
    loading: false,
    emptyText: '暂无相关订单'
  },

  onLoad() {
    this.loadData()
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadData()
  },

  onPullDownRefresh() {
    this.loadData().then(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 加载数据
  loadData() {
    return new Promise((resolve) => {
      this.setData({
        loading: true
      })

      // 模拟网络请求延迟
      setTimeout(() => {
        this.setData({
          orders: this.filterOrders(mockData.orders),
          recommendProducts: mockData.products.slice(0, 5).map(item => ({
            ...item,
            tag: item.groupType
          })),
          loading: false
        })
        resolve()
      }, 500)
    })
  },

  // 根据状态筛选订单
  filterOrders(orders) {
    let filteredOrders = orders

    // 根据状态筛选
    if (this.data.currentStatus !== 'all') {
      filteredOrders = orders.filter(order => order.status === this.data.currentStatus)
    }

    // 根据搜索关键词筛选
    if (this.data.searchKeyword) {
      const keyword = this.data.searchKeyword.toLowerCase()
      filteredOrders = filteredOrders.filter(order => 
        order.products.some(product => 
          product.title.toLowerCase().includes(keyword)
        )
      )
    }

    return filteredOrders
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    })
    
    // 防抖搜索
    clearTimeout(this.searchTimer)
    this.searchTimer = setTimeout(() => {
      this.loadData()
    }, 500)
  },

  // 状态标签点击
  onStatusTap(e) {
    const status = e.currentTarget.dataset.status
    this.setData({
      currentStatus: status
    })
    this.loadData()
  },

  // 跳转到商品详情
  goToProduct(e) {
    const productId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/product/product?id=${productId}`
    })
  },

  // 取消订单
  cancelOrder(e) {
    const orderId = e.currentTarget.dataset.id
    
    wx.showModal({
      title: '确认取消',
      content: '确定要取消这个订单吗？',
      success: (res) => {
        if (res.confirm) {
          // 这里应该调用API取消订单
          console.log('取消订单:', orderId)
          getApp().showToast('订单已取消', 'success')
          this.loadData()
        }
      }
    })
  },

  // 支付订单
  payOrder(e) {
    const orderId = e.currentTarget.dataset.id
    
    wx.showLoading({
      title: '支付中...'
    })

    // 模拟支付过程
    setTimeout(() => {
      wx.hideLoading()
      
      // 这里应该调用微信支付API
      wx.requestPayment({
        timeStamp: Date.now().toString(),
        nonceStr: 'randomstring',
        package: 'prepay_id=mock_prepay_id',
        signType: 'MD5',
        paySign: 'mock_pay_sign',
        success: (res) => {
          getApp().showToast('支付成功', 'success')
          this.loadData()
        },
        fail: (res) => {
          if (res.errMsg !== 'requestPayment:fail cancel') {
            getApp().showToast('支付失败')
          }
        }
      })
    }, 1000)
  },

  // 确认收货
  confirmReceive(e) {
    const orderId = e.currentTarget.dataset.id
    
    wx.showModal({
      title: '确认收货',
      content: '确认已收到商品吗？',
      success: (res) => {
        if (res.confirm) {
          // 这里应该调用API确认收货
          console.log('确认收货:', orderId)
          getApp().showToast('确认收货成功', 'success')
          this.loadData()
        }
      }
    })
  },

  // 查看订单详情
  viewOrderDetail(e) {
    const orderId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/order-detail/order-detail?id=${orderId}`
    })
  },

  // 回到首页
  goToHome() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  }
})
