// components/product-card/product-card.js
Component({
  properties: {
    product: {
      type: Object,
      value: {}
    },
    showStats: {
      type: Boolean,
      value: true
    }
  },

  methods: {
    onTap() {
      const product = this.data.product
      if (product && product.id) {
        wx.navigateTo({
          url: `/pages/product/product?id=${product.id}`
        })
      }
    }
  }
})
