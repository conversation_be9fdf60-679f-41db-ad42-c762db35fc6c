/* pages/product/product.wxss */
.container {
  padding-bottom: 120rpx;
}

/* 商品图片 */
.product-images {
  height: 750rpx;
}

.image-swiper {
  height: 100%;
}

.product-image {
  width: 100%;
  height: 100%;
}

/* 商品基本信息 */
.product-basic-info {
  margin: 20rpx;
}

.product-title {
  font-size: 32rpx;
  font-weight: bold;
  line-height: 1.4;
  margin-bottom: 20rpx;
}

.product-price {
  margin-bottom: 15rpx;
}

.current-price {
  color: #fa5151;
  font-size: 36rpx;
  font-weight: bold;
  margin-right: 15rpx;
}

.original-price {
  color: #999999;
  font-size: 28rpx;
  text-decoration: line-through;
}

.group-info {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.group-tag {
  background-color: #ff6b6b;
  color: white;
  padding: 5rpx 15rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  margin-right: 15rpx;
}

.group-detail {
  color: #666666;
  font-size: 26rpx;
}

.product-stats {
  display: flex;
  gap: 30rpx;
  color: #999999;
  font-size: 24rpx;
}

/* 规格选择 */
.product-specs {
  margin: 20rpx;
}

.specs-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.spec-group {
  margin-bottom: 30rpx;
}

.spec-name {
  font-size: 28rpx;
  margin-bottom: 15rpx;
  color: #333333;
}

.spec-options {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.spec-option {
  padding: 15rpx 25rpx;
  border: 1rpx solid #e5e5e5;
  border-radius: 10rpx;
  font-size: 26rpx;
  color: #666666;
  background-color: #f9f9f9;
}

.spec-option.active {
  border-color: #07c160;
  background-color: #07c160;
  color: white;
}

/* 商品详情 */
.product-detail {
  margin: 20rpx;
}

.detail-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.detail-description {
  font-size: 28rpx;
  line-height: 1.6;
  color: #666666;
  margin-bottom: 30rpx;
}

.detail-images {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.detail-image {
  width: 100%;
  border-radius: 10rpx;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  padding: 20rpx;
  border-top: 1rpx solid #e5e5e5;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 100;
}

.action-left {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: none;
  border: none;
  padding: 10rpx;
  font-size: 22rpx;
  color: #666666;
}

.action-btn icon {
  margin-bottom: 5rpx;
}

.action-right {
  display: flex;
  gap: 20rpx;
}

.cart-btn, .buy-btn {
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  border: none;
}

.cart-btn {
  background-color: #ff976a;
  color: white;
}

.buy-btn {
  background-color: #07c160;
  color: white;
}

/* 规格选择弹窗 */
.spec-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.spec-modal.show {
  opacity: 1;
  visibility: visible;
}

.spec-modal-content {
  background-color: white;
  border-radius: 20rpx 20rpx 0 0;
  padding: 40rpx;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.spec-modal.show .spec-modal-content {
  transform: translateY(0);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 40rpx;
}

.modal-product-info {
  display: flex;
  flex: 1;
}

.modal-product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
}

.modal-product-detail {
  flex: 1;
}

.modal-product-price {
  color: #fa5151;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.modal-selected-specs {
  color: #666666;
  font-size: 24rpx;
}

.modal-close {
  font-size: 40rpx;
  color: #999999;
  padding: 10rpx;
  line-height: 1;
}

.modal-specs {
  margin-bottom: 40rpx;
}

.quantity-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 40rpx;
}

.quantity-label {
  font-size: 28rpx;
  color: #333333;
}

.quantity-controls {
  display: flex;
  align-items: center;
  border: 1rpx solid #e5e5e5;
  border-radius: 10rpx;
  overflow: hidden;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  background-color: #f5f5f5;
  border: none;
  font-size: 28rpx;
  color: #333333;
}

.quantity-btn:disabled {
  color: #ccc;
}

.quantity-input {
  width: 80rpx;
  height: 60rpx;
  text-align: center;
  font-size: 28rpx;
  border: none;
  background-color: white;
}

.modal-actions {
  display: flex;
  gap: 20rpx;
}

.modal-cart-btn, .modal-buy-btn {
  flex: 1;
  padding: 25rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  border: none;
  text-align: center;
}

.modal-cart-btn {
  background-color: #ff976a;
  color: white;
}

.modal-buy-btn {
  background-color: #07c160;
  color: white;
}
