// pages/index/index.js
const mockData = require('../../data/mock.js')
const util = require('../../utils/util.js')

Page({
  data: {
    banners: [],
    categories: [],
    products: [],
    currentCategory: 1,
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10
  },

  onLoad() {
    this.loadData()
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadData()
  },

  onPullDownRefresh() {
    this.setData({
      page: 1,
      hasMore: true,
      products: []
    })
    this.loadData().then(() => {
      wx.stopPullDownRefresh()
    })
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreProducts()
    }
  },

  // 加载数据
  loadData() {
    return new Promise((resolve) => {
      this.setData({
        loading: true
      })

      // 模拟网络请求延迟
      setTimeout(() => {
        const products = mockData.products.map(item => ({
          ...item,
          publishTimeText: util.formatRelativeTime(item.publishTime),
          buyers: this.generateBuyerAvatars(item.sales)
        }))

        this.setData({
          banners: mockData.banners,
          categories: mockData.categories,
          products: products,
          loading: false
        })
        resolve()
      }, 500)
    })
  },

  // 加载更多商品
  loadMoreProducts() {
    if (this.data.loading) return

    this.setData({
      loading: true,
      page: this.data.page + 1
    })

    // 模拟加载更多数据
    setTimeout(() => {
      // 这里可以根据实际情况判断是否还有更多数据
      const hasMore = this.data.page < 3 // 假设只有3页数据
      
      if (hasMore) {
        // 复制现有商品数据作为新数据
        const newProducts = mockData.products.map((item, index) => ({
          ...item,
          id: item.id + this.data.page * 100 + index,
          publishTimeText: util.formatRelativeTime(item.publishTime - this.data.page * 60 * 60 * 1000),
          buyers: this.generateBuyerAvatars(item.sales)
        }))

        this.setData({
          products: [...this.data.products, ...newProducts],
          loading: false,
          hasMore: this.data.page < 2
        })
      } else {
        this.setData({
          loading: false,
          hasMore: false
        })
      }
    }, 800)
  },

  // 生成买家头像
  generateBuyerAvatars(count) {
    const avatars = []
    const maxShow = Math.min(count, 3)
    for (let i = 0; i < maxShow; i++) {
      avatars.push(`https://via.placeholder.com/40x40/07c160/ffffff?text=${i + 1}`)
    }
    return avatars
  },

  // 搜索
  goToSearch() {
    wx.navigateTo({
      url: '/pages/search/search'
    })
  },

  // 消息
  goToMessage() {
    wx.switchTab({
      url: '/pages/message/message'
    })
  },

  // 轮播图点击
  onBannerTap(e) {
    const link = e.currentTarget.dataset.link
    if (link) {
      wx.navigateTo({
        url: link
      })
    }
  },

  // 分类点击
  onCategoryTap(e) {
    const categoryId = e.currentTarget.dataset.id
    this.setData({
      currentCategory: categoryId,
      page: 1,
      products: []
    })
    this.loadData()
  },

  // 商品详情
  goToProduct(e) {
    const productId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/product/product?id=${productId}`
    })
  },

  // 分享商品
  onShareProduct(e) {
    const productId = e.currentTarget.dataset.id
    const product = this.data.products.find(p => p.id === productId)
    
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })

    getApp().showToast(`分享商品：${product.title}`)
  },

  // 购买商品
  onBuyProduct(e) {
    const productId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/product/product?id=${productId}&action=buy`
    })
  },

  // 分享给朋友
  onShareAppMessage() {
    return {
      title: '快团团 - 优质团购平台',
      path: '/pages/index/index',
      imageUrl: '/images/share-logo.png'
    }
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '快团团 - 发现更多优质商品',
      imageUrl: '/images/share-logo.png'
    }
  }
})
