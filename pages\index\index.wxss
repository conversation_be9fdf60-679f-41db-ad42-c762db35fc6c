/* pages/index/index.wxss */
.container {
  padding: 0;
  background-color: #f5f5f5;
}

/* 搜索栏 */
.search-bar {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: white;
  border-bottom: 1rpx solid #e5e5e5;
}

.search-input {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 50rpx;
  padding: 15rpx 20rpx;
  margin-right: 20rpx;
}

.search-icon {
  margin-right: 10rpx;
}

.search-placeholder {
  color: #999999;
  font-size: 28rpx;
}

.message-icon {
  padding: 10rpx;
}

/* 轮播图 */
.banner-section {
  margin: 20rpx;
  border-radius: 10rpx;
  overflow: hidden;
}

.banner-swiper {
  height: 300rpx;
}

.banner-image {
  width: 100%;
  height: 100%;
}

/* 分类导航 */
.category-nav {
  background-color: white;
  margin: 20rpx;
  border-radius: 10rpx;
  padding: 20rpx 0;
}

.category-scroll {
  white-space: nowrap;
}

.category-list {
  display: inline-flex;
  padding: 0 20rpx;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 40rpx;
  padding: 10rpx;
  border-radius: 10rpx;
  min-width: 80rpx;
}

.category-item.active {
  background-color: #07c160;
  color: white;
}

.category-icon {
  font-size: 32rpx;
  margin-bottom: 5rpx;
}

.category-name {
  font-size: 24rpx;
}

/* 商品列表 */
.product-list {
  padding: 0 20rpx;
}

.product-item {
  background-color: white;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.product-image-container {
  position: relative;
  height: 400rpx;
}

.product-image {
  width: 100%;
  height: 100%;
}

.product-tag {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  background-color: rgba(255, 107, 107, 0.9);
  color: white;
  padding: 5rpx 15rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
}

.product-info {
  padding: 20rpx;
}

.product-title {
  font-size: 28rpx;
  font-weight: bold;
  line-height: 1.4;
  margin-bottom: 15rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.product-price {
  margin-bottom: 15rpx;
}

.current-price {
  color: #fa5151;
  font-size: 32rpx;
  font-weight: bold;
  margin-right: 10rpx;
}

.original-price {
  color: #999999;
  font-size: 24rpx;
  text-decoration: line-through;
  margin-right: 10rpx;
}

.group-info {
  color: #666666;
  font-size: 24rpx;
}

.product-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.stats-left {
  display: flex;
  align-items: center;
}

.avatar {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  margin-right: 5rpx;
  border: 2rpx solid white;
}

.stats-text {
  color: #666666;
  font-size: 24rpx;
  margin-left: 10rpx;
}

.publish-time {
  color: #999999;
  font-size: 22rpx;
}

.product-actions {
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
}

.share-btn {
  background-color: #f5f5f5;
  color: #333333;
  border: none;
  border-radius: 30rpx;
  padding: 10rpx 30rpx;
  font-size: 24rpx;
}

.buy-btn {
  background-color: #07c160;
  color: white;
  border: none;
  border-radius: 30rpx;
  padding: 10rpx 30rpx;
  font-size: 24rpx;
}

/* 加载状态 */
.load-more, .no-more {
  text-align: center;
  padding: 40rpx;
}

.load-text, .no-more-text {
  color: #999999;
  font-size: 24rpx;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 20rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  color: #999999;
  font-size: 28rpx;
}
