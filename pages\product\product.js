// pages/product/product.js
const mockData = require('../../data/mock.js')
const util = require('../../utils/util.js')

Page({
  data: {
    product: {},
    selectedSpecs: {},
    selectedSpecsText: '',
    quantity: 1,
    showSpecModal: false,
    currentAction: '' // 'cart' 或 'buy'
  },

  onLoad(options) {
    const productId = parseInt(options.id)
    const action = options.action
    
    this.loadProduct(productId)
    
    if (action === 'buy') {
      // 直接显示购买弹窗
      setTimeout(() => {
        this.showSpecModal('buy')
      }, 500)
    }
  },

  // 加载商品详情
  loadProduct(productId) {
    // 从模拟数据中查找商品
    const product = mockData.products.find(p => p.id === productId)
    
    if (product) {
      // 初始化默认规格选择
      const selectedSpecs = {}
      if (product.specs) {
        product.specs.forEach(spec => {
          selectedSpecs[spec.name] = spec.options[0]
        })
      }

      this.setData({
        product: {
          ...product,
          publishTimeText: util.formatRelativeTime(product.publishTime)
        },
        selectedSpecs: selectedSpecs
      })
      
      this.updateSelectedSpecsText()
      
      // 设置页面标题
      wx.setNavigationBarTitle({
        title: product.title.length > 10 ? product.title.substring(0, 10) + '...' : product.title
      })
    } else {
      getApp().showToast('商品不存在')
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  // 预览图片
  previewImage(e) {
    const src = e.currentTarget.dataset.src
    const urls = this.data.product.images || [this.data.product.image]
    
    wx.previewImage({
      current: src,
      urls: urls
    })
  },

  // 规格选择
  onSpecTap(e) {
    const specName = e.currentTarget.dataset.spec
    const option = e.currentTarget.dataset.option
    
    this.setData({
      [`selectedSpecs.${specName}`]: option
    })
    
    this.updateSelectedSpecsText()
  },

  // 更新已选规格文本
  updateSelectedSpecsText() {
    const specs = this.data.selectedSpecs
    const specTexts = Object.keys(specs).map(key => specs[key])
    
    this.setData({
      selectedSpecsText: specTexts.join(' ')
    })
  },

  // 显示规格选择弹窗
  showSpecModal(action) {
    this.setData({
      showSpecModal: true,
      currentAction: action
    })
  },

  // 隐藏规格选择弹窗
  hideSpecModal() {
    this.setData({
      showSpecModal: false
    })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 空函数，用于阻止事件冒泡
  },

  // 数量减少
  decreaseQuantity() {
    if (this.data.quantity > 1) {
      this.setData({
        quantity: this.data.quantity - 1
      })
    }
  },

  // 数量增加
  increaseQuantity() {
    this.setData({
      quantity: this.data.quantity + 1
    })
  },

  // 数量输入
  onQuantityInput(e) {
    const value = parseInt(e.detail.value) || 1
    this.setData({
      quantity: Math.max(1, value)
    })
  },

  // 加入购物车
  addToCart() {
    if (this.data.product.specs && this.data.product.specs.length > 0) {
      this.showSpecModal('cart')
    } else {
      this.confirmAddToCart()
    }
  },

  // 立即购买
  buyNow() {
    if (this.data.product.specs && this.data.product.specs.length > 0) {
      this.showSpecModal('buy')
    } else {
      this.confirmBuyNow()
    }
  },

  // 确认加入购物车
  confirmAddToCart() {
    const { product, selectedSpecs, quantity } = this.data
    
    // 这里应该调用API将商品加入购物车
    console.log('加入购物车:', {
      productId: product.id,
      specs: selectedSpecs,
      quantity: quantity
    })
    
    getApp().showToast('已加入购物车', 'success')
    this.hideSpecModal()
  },

  // 确认立即购买
  confirmBuyNow() {
    const { product, selectedSpecs, quantity } = this.data
    
    // 这里应该跳转到订单确认页面
    console.log('立即购买:', {
      productId: product.id,
      specs: selectedSpecs,
      quantity: quantity
    })
    
    // 模拟跳转到订单确认页面
    wx.navigateTo({
      url: `/pages/order-confirm/order-confirm?productId=${product.id}&quantity=${quantity}&specs=${JSON.stringify(selectedSpecs)}`
    })
    
    this.hideSpecModal()
  },

  // 分享给朋友
  onShareAppMessage() {
    const product = this.data.product
    return {
      title: product.title,
      path: `/pages/product/product?id=${product.id}`,
      imageUrl: product.image
    }
  },

  // 分享到朋友圈
  onShareTimeline() {
    const product = this.data.product
    return {
      title: product.title,
      imageUrl: product.image
    }
  }
})
