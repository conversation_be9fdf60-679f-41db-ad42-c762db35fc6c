// pages/message/message.js
const mockData = require('../../data/mock.js')
const util = require('../../utils/util.js')

Page({
  data: {
    currentTab: 'member',
    searchKeyword: '',
    messages: [],
    recommendGroups: [
      {
        id: 1,
        title: '图图妈高品质团购',
        subtitle: '超多团头等',
        avatar: 'https://via.placeholder.com/80x80/ff976a/ffffff?text=图图'
      }
    ],
    featuredProduct: {
      image: 'https://via.placeholder.com/120x120/ffeb3b/333333?text=桃子',
      title: '🔴血推荐🔴【高甜露天黄肉油桃】秒杀4斤🍑桃香浓郁❤️清甜多汁',
      participants: 919,
      views: 1277
    }
  },

  onLoad() {
    this.loadMessages()
  },

  onShow() {
    // 页面显示时刷新消息
    this.loadMessages()
  },

  // 加载消息数据
  loadMessages() {
    const messages = mockData.messages.map(msg => ({
      ...msg,
      timeText: util.formatRelativeTime(msg.time)
    }))

    this.setData({
      messages: messages
    })
  },

  // 切换标签
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab
    this.setData({
      currentTab: tab
    })
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    })
    
    // 防抖搜索
    clearTimeout(this.searchTimer)
    this.searchTimer = setTimeout(() => {
      this.searchGroups()
    }, 500)
  },

  // 搜索团购
  searchGroups() {
    const keyword = this.data.searchKeyword
    if (!keyword) {
      return
    }
    
    // 这里应该调用搜索API
    console.log('搜索团购:', keyword)
    getApp().showToast('搜索功能开发中')
  },

  // 物流助手
  goToLogistics() {
    wx.navigateTo({
      url: '/pages/logistics/logistics'
    })
  },

  // 营销通知
  goToMarketing() {
    wx.navigateTo({
      url: '/pages/marketing/marketing'
    })
  },

  // 优惠消息
  goToPromotion() {
    wx.navigateTo({
      url: '/pages/promotion/promotion'
    })
  },

  // 系统消息
  goToSystem() {
    wx.navigateTo({
      url: '/pages/system-message/system-message'
    })
  },

  // 订阅团购
  subscribeGroup(e) {
    const groupId = e.currentTarget.dataset.id
    
    wx.showModal({
      title: '订阅确认',
      content: '订阅后可以及时收到该团长的团购信息',
      success: (res) => {
        if (res.confirm) {
          // 这里应该调用订阅API
          console.log('订阅团购:', groupId)
          getApp().showToast('订阅成功', 'success')
        }
      }
    })
  },

  // 分享商品
  shareProduct() {
    const product = this.data.featuredProduct
    
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })

    getApp().showToast('分享商品')
  },

  // 购买商品
  buyProduct() {
    const product = this.data.featuredProduct
    
    // 跳转到商品详情页
    wx.navigateTo({
      url: '/pages/product/product?id=1'
    })
  },

  // 读取消息
  readMessage(e) {
    const messageId = e.currentTarget.dataset.id
    const messages = this.data.messages.map(msg => {
      if (msg.id === messageId) {
        return { ...msg, read: true }
      }
      return msg
    })

    this.setData({
      messages: messages
    })

    // 这里应该调用API标记消息为已读
    console.log('标记消息已读:', messageId)

    // 跳转到消息详情页
    wx.navigateTo({
      url: `/pages/message-detail/message-detail?id=${messageId}`
    })
  },

  // 分享给朋友
  onShareAppMessage() {
    return {
      title: '快团团 - 发现更多优质团购',
      path: '/pages/message/message',
      imageUrl: '/images/share-logo.png'
    }
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '快团团 - 优质团购平台',
      imageUrl: '/images/share-logo.png'
    }
  }
})
