<!--pages/product/product.wxml-->
<view class="container">
  <!-- 商品图片轮播 -->
  <view class="product-images">
    <swiper class="image-swiper" indicator-dots="{{true}}" autoplay="{{false}}">
      <swiper-item wx:for="{{product.images}}" wx:key="*this">
        <image class="product-image" src="{{item}}" mode="aspectFill" bindtap="previewImage" data-src="{{item}}"></image>
      </swiper-item>
    </swiper>
  </view>

  <!-- 商品基本信息 -->
  <view class="product-basic-info card">
    <view class="product-title">{{product.title}}</view>
    <view class="product-price">
      <text class="current-price">¥{{product.price}}</text>
      <text class="original-price" wx:if="{{product.originalPrice}}">¥{{product.originalPrice}}</text>
    </view>
    <view class="group-info">
      <text class="group-tag">{{product.groupType}}</text>
      <text class="group-detail">{{product.groupPrice}}元{{product.groupUnit}}（{{product.groupLimit}}份内）</text>
    </view>
    <view class="product-stats">
      <text class="sales">已售{{product.sales}}份</text>
      <text class="likes">{{product.likes}}人看过</text>
      <text class="publish-time">{{product.publishTimeText}}</text>
    </view>
  </view>

  <!-- 规格选择 -->
  <view class="product-specs card" wx:if="{{product.specs && product.specs.length > 0}}">
    <view class="specs-title">选择规格</view>
    <view class="spec-group" wx:for="{{product.specs}}" wx:key="name" wx:for-item="spec">
      <view class="spec-name">{{spec.name}}</view>
      <view class="spec-options">
        <view class="spec-option {{selectedSpecs[spec.name] === option ? 'active' : ''}}" 
              wx:for="{{spec.options}}" wx:key="*this" wx:for-item="option"
              bindtap="onSpecTap" data-spec="{{spec.name}}" data-option="{{option}}">
          {{option}}
        </view>
      </view>
    </view>
  </view>

  <!-- 商品详情 -->
  <view class="product-detail card">
    <view class="detail-title">商品详情</view>
    <view class="detail-description">{{product.detail.description}}</view>
    <view class="detail-images">
      <image class="detail-image" wx:for="{{product.detail.images}}" wx:key="*this" 
             src="{{item}}" mode="widthFix" bindtap="previewImage" data-src="{{item}}"></image>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions">
    <view class="action-left">
      <button class="action-btn" open-type="share">
        <icon type="info" size="20"></icon>
        <text>分享</text>
      </button>
      <button class="action-btn" bindtap="addToCart">
        <icon type="success" size="20"></icon>
        <text>收藏</text>
      </button>
    </view>
    <view class="action-right">
      <button class="cart-btn" bindtap="addToCart">加入购物车</button>
      <button class="buy-btn" bindtap="buyNow">立即购买</button>
    </view>
  </view>
</view>

<!-- 规格选择弹窗 -->
<view class="spec-modal {{showSpecModal ? 'show' : ''}}" bindtap="hideSpecModal">
  <view class="spec-modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <view class="modal-product-info">
        <image class="modal-product-image" src="{{product.image}}" mode="aspectFill"></image>
        <view class="modal-product-detail">
          <view class="modal-product-price">¥{{product.price}}</view>
          <view class="modal-selected-specs">{{selectedSpecsText}}</view>
        </view>
      </view>
      <view class="modal-close" bindtap="hideSpecModal">×</view>
    </view>
    
    <view class="modal-specs">
      <view class="spec-group" wx:for="{{product.specs}}" wx:key="name" wx:for-item="spec">
        <view class="spec-name">{{spec.name}}</view>
        <view class="spec-options">
          <view class="spec-option {{selectedSpecs[spec.name] === option ? 'active' : ''}}" 
                wx:for="{{spec.options}}" wx:key="*this" wx:for-item="option"
                bindtap="onSpecTap" data-spec="{{spec.name}}" data-option="{{option}}">
            {{option}}
          </view>
        </view>
      </view>
    </view>

    <view class="quantity-selector">
      <text class="quantity-label">数量</text>
      <view class="quantity-controls">
        <button class="quantity-btn" bindtap="decreaseQuantity" disabled="{{quantity <= 1}}">-</button>
        <input class="quantity-input" type="number" value="{{quantity}}" bindinput="onQuantityInput"></input>
        <button class="quantity-btn" bindtap="increaseQuantity">+</button>
      </view>
    </view>

    <view class="modal-actions">
      <button class="modal-cart-btn" bindtap="confirmAddToCart">加入购物车</button>
      <button class="modal-buy-btn" bindtap="confirmBuyNow">立即购买</button>
    </view>
  </view>
</view>
