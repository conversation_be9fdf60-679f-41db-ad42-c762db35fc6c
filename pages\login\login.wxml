<!--pages/login/login.wxml-->
<view class="container">
  <!-- Logo区域 -->
  <view class="logo-section">
    <image class="logo" src="/images/logo.png" mode="aspectFit"></image>
    <text class="app-name">快团团</text>
    <text class="app-slogan">优质团购，一键开团</text>
  </view>

  <!-- 登录表单 -->
  <view class="login-form">
    <view class="input-group">
      <view class="input-item">
        <icon class="input-icon" type="info" size="20" color="#999"></icon>
        <input class="input-field" type="text" placeholder="请输入手机号" value="{{phone}}" bindinput="onPhoneInput" maxlength="11" />
      </view>
      
      <view class="input-item">
        <icon class="input-icon" type="success" size="20" color="#999"></icon>
        <input class="input-field" type="number" placeholder="请输入验证码" value="{{code}}" bindinput="onCodeInput" maxlength="6" />
        <button class="code-btn {{canSendCode ? '' : 'disabled'}}" size="mini" bindtap="sendCode" disabled="{{!canSendCode}}">
          {{codeText}}
        </button>
      </view>
    </view>

    <button class="login-btn {{canLogin ? 'active' : ''}}" bindtap="login" disabled="{{!canLogin}}">
      登录
    </button>

    <view class="agreement">
      <checkbox class="agreement-checkbox" checked="{{agreedToTerms}}" bindchange="onAgreementChange"></checkbox>
      <text class="agreement-text">我已阅读并同意</text>
      <text class="agreement-link" bindtap="viewTerms">《用户协议》</text>
      <text class="agreement-text">和</text>
      <text class="agreement-link" bindtap="viewPrivacy">《隐私政策》</text>
    </view>
  </view>

  <!-- 微信登录 -->
  <view class="wechat-login">
    <view class="divider">
      <text class="divider-text">或</text>
    </view>
    <button class="wechat-btn" open-type="getUserInfo" bindgetuserinfo="onWechatLogin">
      <icon type="info" size="20" color="#07c160"></icon>
      <text>微信快速登录</text>
    </button>
  </view>

  <!-- 底部提示 -->
  <view class="bottom-tips">
    <text class="tips-text">登录即表示同意快团团的服务条款</text>
  </view>
</view>
